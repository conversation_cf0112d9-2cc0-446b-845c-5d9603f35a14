Collecting win10toast
  Downloading win10toast-0.9-py2.py3-none-any.whl.metadata (2.1 kB)
Collecting pypiwin32 (from win10toast)
  Downloading pypiwin32-223-py3-none-any.whl.metadata (236 bytes)
Requirement already satisfied: setuptools in c:\users\<USER>\appdata\local\programs\python\python313\lib\site-packages (from win10toast) (80.9.0)
Collecting pywin32>=223 (from pypiwin32->win10toast)
  Downloading pywin32-311-cp313-cp313-win_amd64.whl.metadata (10 kB)
Downloading win10toast-0.9-py2.py3-none-any.whl (21 kB)
Downloading pypiwin32-223-py3-none-any.whl (1.7 kB)
Downloading pywin32-311-cp313-cp313-win_amd64.whl (9.5 MB)
   ---------------------------------------- 9.5/9.5 MB 19.2 MB/s eta 0:00:00
Installing collected packages: pywin32, pypiwin32, win10toast
Successfully installed pypiwin32-223 pywin32-311 win10toast-0.9
