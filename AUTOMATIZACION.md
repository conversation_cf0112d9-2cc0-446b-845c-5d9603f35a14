# 🤖 AUTOMATIZACIÓN - Night Crows Tracker

## 🚀 Inicio Rápido

### Opción 1: Archivos .bat (Windows)
- **ejecutar_tracker.bat** - Abrir la aplicación principal
- **ejecutar_automatico.bat** - Iniciar modo automático
- **verificar_precios.bat** - Verificación manual de precios
- **importar_datos.bat** - Importar datos desde CSV

### Opción 2: Línea de comandos
```bash
# Aplicación principal
python main.py

# Modo automático (ejecuta tareas programadas)
python auto_tracker.py schedule

# Verificación manual
python auto_tracker.py check

# Importar datos
python auto_tracker.py import
```

## 🔧 Funciones Automáticas

### 1. **Alertas de Precios**
- ✅ Monitoreo automático cada hora
- ✅ Alertas cuando el precio cambia más del 15%
- ✅ Recomendaciones automáticas de compra/venta
- ✅ Notificaciones por email (opcional)

### 2. **Reportes Automáticos**
- ✅ Reporte diario a las 8:00 AM
- ✅ Gráficos automáticos generados
- ✅ Resumen de tendencias
- ✅ Archivos guardados en carpeta `reports/`

### 3. **Importación de Datos**
- ✅ Importación automática desde CSV
- ✅ Plantilla CSV incluida
- ✅ Procesamiento diario a las 9:00 AM

### 4. **Análisis Inteligente**
- ✅ Cálculo automático de tendencias
- ✅ Detección de patrones de precios
- ✅ Recomendaciones basadas en estadísticas

## 📊 Importar Datos Masivamente

### Usar el archivo CSV de ejemplo:
1. Edita `data/sample_data.csv` con tus datos reales
2. Ejecuta `importar_datos.bat` o `python auto_tracker.py import`

### Formato del CSV:
```csv
item_name,price,quantity,server,date,notes
Espada de Hierro,105.50,3,Servidor 1,2024-01-15,Precio matutino
Armadura de Cuero,78.25,5,Servidor 2,2024-01-15,Precio vespertino
```

## ⚙️ Configuración

### Editar configuración automática:
Archivo: `config/auto_config.json`

```json
{
  "auto_alerts": {
    "enabled": true,
    "price_change_threshold": 15,
    "email_notifications": false
  },
  "watched_items": [
    "Espada de Hierro",
    "Armadura de Cuero"
  ]
}
```

### Configurar notificaciones por email:
1. Edita `config/auto_config.json`
2. Cambia `email_notifications` a `true`
3. Configura tu email y contraseña de aplicación

## 📈 Archivos Generados Automáticamente

- `reports/daily_YYYYMMDD.json` - Reportes diarios
- `auto_charts/YYYYMMDD/` - Gráficos automáticos
- `data/alerts_YYYYMMDD.json` - Historial de alertas

## 🎯 Casos de Uso

### Para traders activos:
```bash
# Ejecutar modo automático en segundo plano
python auto_tracker.py schedule
```

### Para análisis diario:
```bash
# Verificación rápida manual
python auto_tracker.py check
```

### Para importar datos históricos:
```bash
# Crear plantilla CSV
python auto_tracker.py template

# Importar datos
python auto_tracker.py import mi_archivo.csv
```

## 🔄 Flujo de Trabajo Recomendado

1. **Configuración inicial**: Ejecutar `setup_automation.py`
2. **Datos históricos**: Importar CSV con datos pasados
3. **Modo automático**: Dejar corriendo `ejecutar_automatico.bat`
4. **Revisión diaria**: Verificar reportes en carpeta `reports/`
5. **Alertas**: Revisar alertas importantes para trading

¡El sistema ahora trabaja por ti! 🚀
