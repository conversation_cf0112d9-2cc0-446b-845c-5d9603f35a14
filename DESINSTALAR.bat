@echo off
title Night Crows Tracker - Desinstalador
echo ========================================
echo    DESINSTALADOR NIGHT CROWS TRACKER
echo ========================================
echo.
echo ADVERTENCIA: Esto eliminara todos los datos y configuraciones
echo.
set /p confirm="¿Estas seguro? (s/N): "
if /i not "%confirm%"=="s" (
    echo Desinstalacion cancelada.
    pause
    exit /b
)

echo.
echo Eliminando archivos...

REM Eliminar accesos directos del escritorio
del "%USERPROFILE%\Desktop\Night Crows*.lnk" 2>nul

REM Eliminar directorios de datos (opcional)
echo.
set /p deletedata="¿Eliminar tambien los datos guardados? (s/N): "
if /i "%deletedata%"=="s" (
    rmdir /s /q data 2>nul
    rmdir /s /q reports 2>nul
    rmdir /s /q auto_charts 2>nul
    rmdir /s /q config 2>nul
    echo Datos eliminados.
)

REM Eliminar archivos del sistema
del *.bat 2>nul
del *.py 2>nul
del *.md 2>nul
del requirements.txt 2>nul

echo.
echo ========================================
echo    DESINSTALACION COMPLETADA
echo ========================================
echo.
echo El Night Crows Tracker ha sido eliminado.
echo Gracias por usar nuestro sistema!
echo.
pause
