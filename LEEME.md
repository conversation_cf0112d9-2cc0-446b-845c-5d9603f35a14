# 🚀 NIGHT CROWS TRACKER - SISTEMA COMPLETO

## 🎯 INICIO RÁPIDO (1 CLICK)

### Para Usuarios Básicos:
- **EJECUTAR_TRACKER.bat** - Aplicación principal con interfaz gráfica
- **VERIFICAR_PRECIOS.bat** - Verificación rápida de precios

### Para Usuarios Avanzados:
- **EJECUTAR_AUTOMATICO.bat** - Modo automático completo
- **DASHBOARD_WEB.bat** - Dashboard web en tiempo real
- **NOTIFICACIONES_MOVIL.bat** - Notificaciones en tu móvil
- **IA_PREDICCIONES.bat** - Predicciones con Inteligencia Artificial

## 🤖 FUNCIONES AUTOMÁTICAS

### ✅ Básicas (Siempre Activas)
- Rastreo diario de precios
- Gráficos automáticos
- Recomendaciones de compra/venta
- Almacenamiento seguro de datos

### ✅ Avanzadas (Configurables)
- **Inteligencia Artificial**: Predicciones de precios con ML
- **Alertas Inteligentes**: Notificaciones cuando hay oportunidades
- **Dashboard Web**: Interfaz moderna accesible desde cualquier dispositivo
- **Notificaciones Móviles**: Telegram, Discord, Push notifications
- **Análisis Técnico**: Detección de patrones y tendencias
- **Importación Masiva**: Procesa cientos de registros desde CSV
- **Portafolio Personal**: Rastrea tus inversiones y ganancias

## 📱 CONFIGURAR NOTIFICACIONES MÓVILES

### Opción 1: ntfy.sh (Más Fácil)
1. Instala la app "ntfy" desde Google Play o App Store
2. Ejecuta: `CONFIGURAR_TODO.bat`
3. Elige un nombre único para tu tema (ej: nightcrows_juan123)
4. Suscríbete al tema en la app

### Opción 2: Telegram
1. Habla con @BotFather en Telegram
2. Crea un bot con `/newbot`
3. Habla con @userinfobot para obtener tu chat_id
4. Ejecuta: `CONFIGURAR_TODO.bat`

## 🌐 DASHBOARD WEB

Ejecuta `DASHBOARD_WEB.bat` y abre: http://localhost:5000

**Características:**
- Gráficos interactivos en tiempo real
- Alertas y señales automáticas
- Compatible con móviles y tablets
- API REST para integraciones
- Agregar precios desde la web

## 🧠 INTELIGENCIA ARTIFICIAL

El sistema incluye:
- **Predicciones de precios** usando Machine Learning
- **Detección de patrones** técnicos automática
- **Señales de trading** con niveles de confianza
- **Análisis de tendencias** avanzado

## 📊 IMPORTAR DATOS MASIVAMENTE

1. Edita `data/sample_data.csv` con tus datos
2. Formato: `item_name,price,quantity,server,date,notes`
3. Ejecuta: `IMPORTAR_DATOS.bat`

## ⚙️ CONFIGURACIÓN AVANZADA

### Archivos de Configuración:
- `config/main_config.json` - Configuración principal
- `config/auto_config.json` - Automatización
- `config/notifications_config.json` - Notificaciones
- `config/trading_config.json` - Trading y portafolio

### Personalizar Alertas:
Edita `config/auto_config.json`:
```json
{
  "auto_alerts": {
    "price_change_threshold": 15,
    "watched_items": ["Tu Objeto Favorito"]
  }
}
```

## 🔄 INICIO AUTOMÁTICO

Para que el sistema funcione automáticamente al encender la PC:
1. Presiona Win+R, escribe `shell:startup`
2. Copia `INICIO_AUTOMATICO.bat` a esa carpeta
3. ¡Listo! El sistema se iniciará automáticamente

## 📈 CASOS DE USO

### Trader Casual:
- Usa `EJECUTAR_TRACKER.bat` para ingresar precios manualmente
- Revisa recomendaciones diarias

### Trader Activo:
- Ejecuta `EJECUTAR_AUTOMATICO.bat` en segundo plano
- Configura notificaciones móviles
- Usa el dashboard web para monitoreo

### Trader Profesional:
- Activa todas las funciones automáticas
- Usa predicciones IA para decisiones
- Configura inicio automático
- Importa datos históricos masivamente

## 🆘 SOLUCIÓN DE PROBLEMAS

### Error: "No se reconoce python"
- Instala Python desde python.org
- Marca "Add Python to PATH" durante instalación

### Error: "Módulo no encontrado"
- Ejecuta: `pip install -r requirements.txt`

### Dashboard web no carga:
- Verifica que el puerto 5000 esté libre
- Ejecuta como administrador si es necesario

### Notificaciones no llegan:
- Ejecuta `CONFIGURAR_TODO.bat` para reconfigurar
- Verifica conexión a internet

## 📞 SOPORTE

El sistema es completamente automático y no requiere mantenimiento.
Todos los datos se guardan localmente en tu computadora.

¡Disfruta del trading automático! 🚀
