# Night Crows - Rast<PERSON><PERSON> de Precios

Un programa para rastrear y analizar los precios de objetos en el juego Night Crows, generando gráficos y estadísticas para ayudarte a decidir cuándo comprar y vender.

## Características

- 📊 **Registro diario de precios**: Ingresa fácilmente los precios de objetos
- 📈 **Gráficos interactivos**: Visualiza tendencias de precios a lo largo del tiempo
- 📉 **Análisis estadístico**: Obtén promedios, máximos, mínimos y tendencias
- 🔍 **Comparación de objetos**: Compara múltiples objetos en un solo gráfico
- 💡 **Recomendaciones**: Recibe sugerencias de cuándo comprar o vender
- 💾 **Almacenamiento local**: Todos los datos se guardan localmente en tu computadora

## Instalación

1. **Instalar Python** (si no lo tienes):
   - Descarga Python desde [python.org](https://python.org)
   - Aseg<PERSON><PERSON> de marcar "Add Python to PATH" durante la instalación

2. **Instalar dependencias**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Ejecutar el programa**:
   ```bash
   python main.py
   ```

## Cómo usar

### 1. Agregar registros diarios
- **Nombre del Objeto**: Escribe el nombre del objeto (ej: "Espada de Hierro")
- **Precio**:<thinking>
He creado todos los archivos necesarios para el programa: requirements.txt, data_manager.py, chart_generator.py y main.py con la interfaz gráfica para ingresar datos, generar gráficos y mostrar recomendaciones.

El siguiente paso es indicarte cómo instalar las dependencias y ejecutar el programa para que puedas probarlo.

No hay procesos activos que interfieran, así que puedo proceder a darte instrucciones para la instalación y ejecución.

</thinking>

<attempt_completion>
<result>
He creado un programa completo para rastrear y analizar las estadísticas de precios de objetos en Night Crows. El programa incluye:

- Interfaz gráfica para ingresar datos diarios de objetos (precio, cantidad, servidor, notas).
- Almacenamiento local de datos en formato JSON.
- Generación de gráficos de precios, estadísticas y comparaciones entre objetos.
- Análisis de tendencias y recomendaciones de compra/venta basadas en los datos históricos.

Para usar el programa, sigue estos pasos:

1. Instala las dependencias con:
   pip install -r requirements.txt

2. Ejecuta la aplicación con:
   python main.py

La interfaz te permitirá agregar registros diarios, seleccionar objetos y visualizar gráficos y recomendaciones para ayudarte a decidir cuándo comprar o vender.

Este programa es una base sólida que puedes ampliar según tus necesidades.

</result>
<command>pip install -r requirements.txt && python main.py</command>
</attempt_completion>
