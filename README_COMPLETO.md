# 🎮 Night Crows - Rast<PERSON><PERSON> de Precios COMPLETO

## 🚀 **SISTEMA COMPLETAMENTE AUTOMÁTICO CON IA**

Un sistema avanzado de rastreo de precios para el juego Night Crows con **Inteligencia Artificial**, **automatización completa** y **funciones de edición/eliminación**.

---

## ✨ **NUEVAS FUNCIONES AGREGADAS**

### 🔧 **GESTIÓN COMPLETA DE DATOS**
- ✅ **Editar registros individuales** - Modifica precio, cantidad, servidor, notas
- ✅ **Eliminar registros específicos** - Borra entradas individuales
- ✅ **Eliminar objetos completos** - Remueve objetos con todos sus registros
- ✅ **Renombrar objetos** - Cambia nombres de objetos existentes
- ✅ **Crear backups automáticos** - Protege tus datos
- ✅ **Buscar objetos** - Encuentra objetos por nombre
- ✅ **Restaurar desde backup** - Recupera datos perdidos

### 🤖 **INTELIGENCIA ARTIFICIAL**
- ✅ **Predicciones de precios** con Machine Learning
- ✅ **Detección automática de patrones** técnicos
- ✅ **Señales de trading** con niveles de confianza
- ✅ **Análisis de tendencias** avanzado

### 📱 **NOTIFICACIONES MÓVILES**
- ✅ **Telegram** - Alertas instantáneas
- ✅ **Discord** - Notificaciones en servidores
- ✅ **ntfy.sh** - Push notifications gratuitas
- ✅ **Pushbullet** - Sincronización multiplataforma

### 🌐 **DASHBOARD WEB EN TIEMPO REAL**
- ✅ **Interfaz moderna** accesible desde cualquier dispositivo
- ✅ **Gráficos interactivos** con Plotly
- ✅ **API REST** para integraciones
- ✅ **Actualización automática** cada 30 segundos

---

## 🎯 **INSTALACIÓN SÚPER FÁCIL**

### **Opción 1: Instalación Automática (RECOMENDADA)**
```bash
python install_everything.py
```
¡Y listo! Todo se instala y configura automáticamente.

### **Opción 2: Instalación Manual**
```bash
pip install -r requirements.txt
python setup_automation.py
```

---

## ⚡ **USO ULTRA-FÁCIL**

### **Para Usuarios Básicos (1 Click):**
- **`EJECUTAR_TRACKER.bat`** - Aplicación principal con interfaz gráfica
- **`VERIFICAR_PRECIOS.bat`** - Verificación rápida de precios

### **Para Usuarios Avanzados (1 Click):**
- **`EJECUTAR_AUTOMATICO.bat`** - Modo automático completo con IA
- **`DASHBOARD_WEB.bat`** - Dashboard web en tiempo real
- **`NOTIFICACIONES_MOVIL.bat`** - Notificaciones móviles
- **`IA_PREDICCIONES.bat`** - Predicciones con Inteligencia Artificial

---

## 🔧 **FUNCIONES DE EDICIÓN Y ELIMINACIÓN**

### **En la Interfaz Gráfica:**

#### **Editar Registros:**
1. Selecciona un objeto
2. Click en **"Editar Registros"**
3. Selecciona el registro a editar
4. Modifica los campos necesarios
5. Guarda los cambios

#### **Eliminar Objetos:**
1. Click en **"Eliminar Objeto"**
2. Selecciona el objeto a eliminar
3. Confirma la eliminación
4. ¡Listo! El objeto y todos sus registros se eliminan

#### **Renombrar Objetos:**
1. Click en **"Renombrar Objeto"**
2. Selecciona el objeto actual
3. Escribe el nuevo nombre
4. Confirma el cambio

#### **Crear Backup:**
1. Click en **"Crear Backup"**
2. Se crea automáticamente un backup con fecha y hora
3. Los backups se guardan en la carpeta `backups/`

### **Desde Código Python:**
```python
from data_manager import DataManager

dm = DataManager()

# Editar una entrada
success, message = dm.edit_item_entry("Espada", 0, new_price=150.0)

# Eliminar una entrada específica
success, message = dm.delete_item_entry("Espada", 1)

# Eliminar objeto completo
success, message = dm.delete_item_completely("Objeto Viejo")

# Renombrar objeto
success, message = dm.rename_item("Nombre Viejo", "Nombre Nuevo")

# Crear backup
backup_file = dm.backup_data()

# Buscar objetos
results = dm.search_items("espada")
```

---

## 🤖 **AUTOMATIZACIÓN COMPLETA**

### **Funciones Automáticas Avanzadas:**

1. **Importación Masiva**: Procesa cientos de registros desde CSV
2. **Alertas Inteligentes**: Detecta oportunidades automáticamente
3. **Reportes Diarios**: Genera análisis completos cada día
4. **Gráficos Automáticos**: Crea visualizaciones sin intervención
5. **Backup Automático**: Protege tus datos automáticamente
6. **Inicio Automático**: Se ejecuta al encender la PC

### **Análisis Automático con IA:**
- **Recomendaciones IA**: "COMPRAR", "VENDER", "MANTENER"
- **Detección de Soporte/Resistencia**: Niveles técnicos automáticos
- **Análisis de Volatilidad**: Identificación de riesgo automática
- **Comparación Multi-objeto**: Análisis comparativo automático

---

## 📊 **IMPORTACIÓN MASIVA DE DATOS**

### **Formato CSV Soportado:**
```csv
item_name,price,quantity,server,date,notes
Espada de Hierro,105.50,3,Servidor 1,2024-01-15,Precio matutino
Armadura de Cuero,78.25,5,Servidor 2,2024-01-15,Precio vespertino
```

### **Importar Datos:**
```bash
# Crear plantilla CSV
python auto_tracker.py template

# Importar desde CSV
python auto_tracker.py import mi_archivo.csv
```

---

## 🌐 **DASHBOARD WEB**

### **Acceder al Dashboard:**
1. Ejecuta: `DASHBOARD_WEB.bat`
2. Abre tu navegador en: `http://localhost:5000`
3. ¡Disfruta de gráficos interactivos en tiempo real!

### **Características del Dashboard:**
- 📊 Gráficos interactivos con Plotly
- 🔄 Actualización automática cada 30 segundos
- 📱 Responsive - funciona en móviles
- 🎨 Interfaz moderna y elegante
- 📈 Múltiples tipos de visualización

---

## 📱 **NOTIFICACIONES MÓVILES**

### **Configurar Telegram:**
1. Crea un bot con @BotFather
2. Obtén tu token y chat ID
3. Configura en `config/mobile_config.json`

### **Configurar Discord:**
1. Crea un webhook en tu servidor
2. Copia la URL del webhook
3. Configura en `config/mobile_config.json`

### **Configurar ntfy.sh (Gratuito):**
1. Elige un nombre único para tu canal
2. Configura en `config/mobile_config.json`
3. Instala la app ntfy en tu móvil

---

## 🧠 **INTELIGENCIA ARTIFICIAL**

### **Predicciones de Precios:**
```bash
python advanced_automation.py predict Espada_de_Hierro
```

### **Análisis Técnico Automático:**
- Detección de patrones de velas
- Niveles de soporte y resistencia
- Indicadores técnicos (RSI, MACD, etc.)
- Señales de compra/venta con confianza

### **Machine Learning:**
- Modelos entrenados con datos históricos
- Predicciones a 1, 3 y 7 días
- Análisis de volatilidad
- Detección de anomalías

---

## 📈 **GRÁFICOS Y ANÁLISIS**

### **Tipos de Gráficos Disponibles:**
1. **Gráfico de Precios** - Evolución temporal con tendencias
2. **Estadísticas** - Barras y distribuciones
3. **Comparación** - Múltiples objetos normalizados
4. **Predicciones IA** - Proyecciones futuras
5. **Análisis Técnico** - Indicadores y señales

### **Análisis Automático:**
- Cálculo de tendencias (Subiendo/Bajando/Estable)
- Recomendaciones de trading
- Detección de oportunidades
- Alertas de cambios significativos

---

## ⚙️ **CONFIGURACIÓN AVANZADA**

### **Archivo: `config/auto_config.json`**
```json
{
  "auto_alerts": {
    "enabled": true,
    "price_change_threshold": 15,
    "email_notifications": false
  },
  "watched_items": [
    "Espada de Hierro",
    "Armadura de Cuero"
  ],
  "ai_predictions": {
    "enabled": true,
    "model_type": "random_forest",
    "prediction_days": [1, 3, 7]
  }
}
```

### **Personalización:**
- Umbral de alertas de precios
- Objetos a monitorear
- Configuración de IA
- Horarios de reportes automáticos
- Configuración de notificaciones

---

## 🔄 **FLUJO DE TRABAJO RECOMENDADO**

### **Para Traders Activos:**
1. **Instalación**: `python install_everything.py`
2. **Configuración**: Editar `config/auto_config.json`
3. **Datos Históricos**: Importar CSV con datos pasados
4. **Modo Automático**: Ejecutar `EJECUTAR_AUTOMATICO.bat`
5. **Dashboard**: Abrir `http://localhost:5000`
6. **Móvil**: Configurar notificaciones Telegram/Discord

### **Para Análisis Ocasional:**
1. **Aplicación Básica**: `EJECUTAR_TRACKER.bat`
2. **Agregar Datos**: Usar interfaz gráfica
3. **Ver Gráficos**: Seleccionar objeto y generar gráficos
4. **Editar/Eliminar**: Usar botones de gestión de datos

---

## 🛠️ **SOLUCIÓN DE PROBLEMAS**

### **Errores Comunes:**

#### **Error de codificación Unicode:**
- ✅ **SOLUCIONADO** - Los emojis problemáticos han sido removidos

#### **Error de importación de módulos:**
```bash
pip install -r requirements.txt
```

#### **Error de permisos:**
- Ejecutar como administrador en Windows
- Usar `sudo` en Linux/Mac

#### **Dashboard no carga:**
- Verificar que el puerto 5000 esté libre
- Ejecutar `DASHBOARD_WEB.bat` como administrador

### **Logs y Depuración:**
- Los logs se guardan en `logs/`
- Los errores se muestran en la consola
- Usar modo verbose: `python main.py --verbose`

---

## 📁 **ESTRUCTURA DE ARCHIVOS**

```
night-crows-tracker/
├── 📄 main.py                    # Aplicación principal
├── 📄 data_manager.py           # Gestión de datos (CON EDITAR/ELIMINAR)
├── 📄 chart_generator.py        # Generación de gráficos
├── 📄 auto_tracker.py           # Automatización básica
├── 📄 advanced_automation.py    # IA y automatización avanzada
├── 📄 web_dashboard.py          # Dashboard web
├── 📄 mobile_notifications.py   # Notificaciones móviles
├── 📄 install_everything.py     # Instalador automático
├── 📄 requirements.txt          # Dependencias
├── 📁 config/                   # Configuraciones
├── 📁 data/                     # Datos de precios
├── 📁 backups/                  # Backups automáticos
├── 📁 reports/                  # Reportes generados
├── 📁 auto_charts/              # Gráficos automáticos
├── 📁 logs/                     # Archivos de log
├── 📁 templates/                # Plantillas web
├── 📁 static/                   # Archivos estáticos web
└── 📄 *.bat                     # Archivos de ejecución fácil
```

---

## 🏆 **CARACTERÍSTICAS ÚNICAS**

### **Ventajas Competitivas:**
1. **Más automático** que cualquier herramienta comercial
2. **Inteligencia Artificial** integrada con predicciones reales
3. **Completamente gratuito** y de código abierto
4. **Datos locales** - privacidad total garantizada
5. **Personalizable** al 100% según tus necesidades
6. **Multiplataforma** - Windows, Mac, Linux
7. **Interfaz en español** - fácil de usar
8. **Soporte completo** para edición y eliminación de datos

### **Casos de Uso:**
- 📈 **Trading profesional** con señales de IA
- 📊 **Análisis de mercado** con gráficos avanzados
- 🤖 **Monitoreo automático** 24/7 sin intervención
- 📱 **Alertas móviles** para oportunidades
- 💾 **Gestión de datos** con backup automático
- 🔧 **Edición flexible** de registros históricos

---

## 🎯 **NIVEL DE AUTOMATIZACIÓN ALCANZADO**

### ⭐⭐⭐⭐⭐ **COMPLETAMENTE AUTOMÁTICO**

El sistema ahora:
- ✅ Se **instala solo** con 1 comando
- ✅ **Funciona solo** 24/7 sin intervención
- ✅ **Aprende solo** con IA y Machine Learning
- ✅ **Notifica solo** cuando hay oportunidades
- ✅ **Analiza solo** todos los datos automáticamente
- ✅ **Decide solo** cuándo comprar/vender
- ✅ **Se edita solo** - puedes modificar cualquier dato
- ✅ **Se respalda solo** - backups automáticos

---

## 🚀 **PRÓXIMAS ACTUALIZACIONES**

### **En Desarrollo:**
- 🔮 **Predicciones más avanzadas** con Deep Learning
- 📊 **Más tipos de gráficos** (velas japonesas, etc.)
- 🌍 **Soporte multi-idioma** (inglés, portugués)
- 📱 **App móvil nativa** para Android/iOS
- 🔗 **Integración con APIs** de exchanges reales
- 🎮 **Soporte para más juegos** similares

---

## 💡 **CONSEJOS PRO**

### **Para Maximizar Ganancias:**
1. **Usa las predicciones de IA** para timing perfecto
2. **Configura alertas móviles** para no perder oportunidades
3. **Analiza patrones históricos** con gráficos comparativos
4. **Mantén backups regulares** de tus datos valiosos
5. **Edita registros erróneos** para mantener precisión
6. **Monitorea múltiples objetos** simultáneamente

### **Para Análisis Avanzado:**
- Combina múltiples indicadores técnicos
- Usa el dashboard web para análisis en tiempo real
- Configura alertas personalizadas por objeto
- Exporta datos para análisis externos
- Utiliza las funciones de búsqueda para encontrar patrones

---

## 📞 **SOPORTE Y COMUNIDAD**

### **¿Necesitas Ayuda?**
- 📖 Lee esta documentación completa
- 🧪 Ejecuta `python test_edit_delete.py` para probar funciones
- 🔍 Revisa los logs en `logs/` para errores
- 💾 Usa los backups para recuperar datos

### **Contribuir al Proyecto:**
- 🐛 Reporta bugs y errores
- 💡 Sugiere nuevas funcionalidades
- 🔧 Contribuye con código
- 📚 Mejora la documentación

---

## 🎉 **¡DISFRUTA DEL TRADING AUTOMÁTICO!**

Con este sistema tienes todo lo necesario para:
- 📈 **Maximizar ganancias** con IA
- ⏰ **Ahorrar tiempo** con automatización
- 📊 **Tomar mejores decisiones** con datos
- 🔧 **Mantener datos limpios** con edición/eliminación
- 📱 **Estar siempre informado** con notificaciones móviles

**¡Es como tener un trader profesional trabajando para ti las 24 horas!** 🤖💰

---

*Última actualización: Enero 2024 - Versión 2.0 con Edición/Eliminación y IA Avanzada*
