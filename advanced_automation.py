#!/usr/bin/env python3
"""
Automatización Avanzada para Night Crows Tracker
Incluye IA, predicciones, web scraping, notificaciones push y más
"""

import os
import json
import time
import requests
from datetime import datetime, timedelta
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
import threading
import sqlite3
import hashlib
from data_manager import DataManager
from chart_generator import ChartGenerator
import matplotlib.pyplot as plt

class AdvancedAutomation:
    def __init__(self):
        self.data_manager = DataManager()
        self.chart_generator = ChartGenerator()
        self.db_path = "data/advanced_tracker.db"
        self.setup_database()
        
    def setup_database(self):
        """Configurar base de datos SQLite para análisis avanzado"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabla para predicciones
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS predictions (
                id INTEGER PRIMARY KEY,
                item_name TEXT,
                predicted_price REAL,
                confidence REAL,
                prediction_date TEXT,
                target_date TEXT,
                actual_price REAL,
                accuracy REAL
            )
        ''')
        
        # Tabla para patrones detectados
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patterns (
                id INTEGER PRIMARY KEY,
                item_name TEXT,
                pattern_type TEXT,
                pattern_data TEXT,
                detected_date TEXT,
                confidence REAL
            )
        ''')
        
        # Tabla para alertas avanzadas
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS advanced_alerts (
                id INTEGER PRIMARY KEY,
                item_name TEXT,
                alert_type TEXT,
                message TEXT,
                priority INTEGER,
                created_date TEXT,
                resolved BOOLEAN DEFAULT FALSE
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def predict_prices_ai(self, item_name, days_ahead=7):
        """Predicción de precios usando Machine Learning"""
        try:
            from sklearn.linear_model import LinearRegression
            from sklearn.preprocessing import PolynomialFeatures
            from sklearn.pipeline import Pipeline
            from sklearn.metrics import mean_absolute_error
        except ImportError:
            print("⚠️ Scikit-learn no disponible. Instalando...")
            os.system("pip install scikit-learn")
            return None
        
        df = self.data_manager.get_item_dataframe(item_name)
        if df.empty or len(df) < 5:
            return None
        
        # Preparar datos
        df['days_since_start'] = (df['date'] - df['date'].min()).dt.days
        X = df[['days_since_start']].values
        y = df['price'].values
        
        # Crear modelo con características polinomiales
        model = Pipeline([
            ('poly', PolynomialFeatures(degree=2)),
            ('linear', LinearRegression())
        ])
        
        # Entrenar modelo
        model.fit(X, y)
        
        # Hacer predicciones
        last_day = X[-1][0]
        future_days = np.array([[last_day + i] for i in range(1, days_ahead + 1)])
        predictions = model.predict(future_days)
        
        # Calcular confianza basada en error histórico
        historical_predictions = model.predict(X)
        mae = mean_absolute_error(y, historical_predictions)
        confidence = max(0, 100 - (mae / np.mean(y)) * 100)
        
        # Guardar predicciones en base de datos
        self.save_predictions(item_name, predictions, confidence, days_ahead)
        
        return {
            'item': item_name,
            'predictions': predictions.tolist(),
            'confidence': confidence,
            'mae': mae,
            'days_ahead': days_ahead
        }
    
    def save_predictions(self, item_name, predictions, confidence, days_ahead):
        """Guardar predicciones en base de datos"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for i, pred_price in enumerate(predictions):
            target_date = (datetime.now() + timedelta(days=i+1)).strftime('%Y-%m-%d')
            cursor.execute('''
                INSERT INTO predictions (item_name, predicted_price, confidence, 
                                      prediction_date, target_date)
                VALUES (?, ?, ?, ?, ?)
            ''', (item_name, float(pred_price), confidence, 
                  datetime.now().strftime('%Y-%m-%d'), target_date))
        
        conn.commit()
        conn.close()
    
    def detect_price_patterns(self, item_name):
        """Detectar patrones en los precios usando análisis técnico"""
        df = self.data_manager.get_item_dataframe(item_name)
        if df.empty or len(df) < 10:
            return []
        
        patterns = []
        prices = df['price'].values
        
        # Detectar soporte y resistencia
        support_level = np.percentile(prices, 25)
        resistance_level = np.percentile(prices, 75)
        current_price = prices[-1]
        
        if current_price <= support_level * 1.05:
            patterns.append({
                'type': 'SOPORTE',
                'message': f'Precio cerca del nivel de soporte ({support_level:.2f})',
                'confidence': 85,
                'action': 'CONSIDERAR COMPRA'
            })
        
        if current_price >= resistance_level * 0.95:
            patterns.append({
                'type': 'RESISTENCIA',
                'message': f'Precio cerca del nivel de resistencia ({resistance_level:.2f})',
                'confidence': 85,
                'action': 'CONSIDERAR VENTA'
            })
        
        # Detectar tendencias
        if len(prices) >= 5:
            recent_trend = np.polyfit(range(5), prices[-5:], 1)[0]
            if abs(recent_trend) > np.std(prices) * 0.1:
                trend_type = "ALCISTA" if recent_trend > 0 else "BAJISTA"
                patterns.append({
                    'type': f'TENDENCIA_{trend_type}',
                    'message': f'Tendencia {trend_type.lower()} detectada',
                    'confidence': 75,
                    'action': 'SEGUIR TENDENCIA'
                })
        
        # Detectar volatilidad
        volatility = np.std(prices) / np.mean(prices) * 100
        if volatility > 15:
            patterns.append({
                'type': 'ALTA_VOLATILIDAD',
                'message': f'Alta volatilidad detectada ({volatility:.1f}%)',
                'confidence': 90,
                'action': 'PRECAUCIÓN'
            })
        
        # Guardar patrones
        self.save_patterns(item_name, patterns)
        return patterns
    
    def save_patterns(self, item_name, patterns):
        """Guardar patrones detectados"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for pattern in patterns:
            cursor.execute('''
                INSERT INTO patterns (item_name, pattern_type, pattern_data, 
                                    detected_date, confidence)
                VALUES (?, ?, ?, ?, ?)
            ''', (item_name, pattern['type'], json.dumps(pattern),
                  datetime.now().strftime('%Y-%m-%d'), pattern['confidence']))
        
        conn.commit()
        conn.close()
    
    def create_trading_signals(self):
        """Generar señales de trading automáticas"""
        signals = []
        items = self.data_manager.get_all_items()
        
        for item in items:
            # Predicciones
            prediction = self.predict_prices_ai(item, days_ahead=3)
            if prediction and prediction['confidence'] > 70:
                current_stats = self.data_manager.get_price_statistics(item)
                if current_stats:
                    current_price = current_stats['current_price']
                    predicted_price = prediction['predictions'][2]  # 3 días adelante
                    
                    change_percent = ((predicted_price - current_price) / current_price) * 100
                    
                    if abs(change_percent) > 10:
                        signal_type = "COMPRA" if change_percent > 0 else "VENTA"
                        signals.append({
                            'item': item,
                            'signal': signal_type,
                            'current_price': current_price,
                            'predicted_price': predicted_price,
                            'change_percent': change_percent,
                            'confidence': prediction['confidence'],
                            'timeframe': '3 días'
                        })
            
            # Patrones técnicos
            patterns = self.detect_price_patterns(item)
            for pattern in patterns:
                if pattern['confidence'] > 80:
                    signals.append({
                        'item': item,
                        'signal': pattern['action'],
                        'reason': pattern['message'],
                        'confidence': pattern['confidence'],
                        'type': 'PATRÓN TÉCNICO'
                    })
        
        return signals
    
    def auto_web_scraper(self, urls=None):
        """Web scraper automático para obtener precios de fuentes externas"""
        if urls is None:
            urls = [
                # Aquí puedes agregar URLs de sitios web de Night Crows
                # "https://nightcrows-market.com/prices",
                # "https://nightcrows-db.com/items"
            ]
        
        scraped_data = []
        
        for url in urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    # Aquí implementarías el parsing específico del sitio
                    # Por ahora, simulamos datos
                    scraped_data.append({
                        'source': url,
                        'timestamp': datetime.now().isoformat(),
                        'status': 'success',
                        'data_points': 0  # Número de precios obtenidos
                    })
            except Exception as e:
                scraped_data.append({
                    'source': url,
                    'timestamp': datetime.now().isoformat(),
                    'status': 'error',
                    'error': str(e)
                })
        
        return scraped_data
    
    def create_portfolio_tracker(self):
        """Crear rastreador de portafolio personal"""
        portfolio_file = "data/portfolio.json"
        
        if not os.path.exists(portfolio_file):
            default_portfolio = {
                "items": {},
                "total_investment": 0,
                "current_value": 0,
                "profit_loss": 0,
                "transactions": []
            }
            
            with open(portfolio_file, 'w', encoding='utf-8') as f:
                json.dump(default_portfolio, f, indent=2, ensure_ascii=False)
        
        return portfolio_file
    
    def calculate_portfolio_performance(self):
        """Calcular rendimiento del portafolio"""
        portfolio_file = self.create_portfolio_tracker()
        
        with open(portfolio_file, 'r', encoding='utf-8') as f:
            portfolio = json.load(f)
        
        total_current_value = 0
        performance_data = {}
        
        for item_name, holdings in portfolio['items'].items():
            current_stats = self.data_manager.get_price_statistics(item_name)
            if current_stats:
                current_price = current_stats['current_price']
                quantity = holdings.get('quantity', 0)
                avg_buy_price = holdings.get('avg_buy_price', 0)
                
                current_value = current_price * quantity
                invested_value = avg_buy_price * quantity
                profit_loss = current_value - invested_value
                profit_loss_percent = (profit_loss / invested_value * 100) if invested_value > 0 else 0
                
                performance_data[item_name] = {
                    'quantity': quantity,
                    'avg_buy_price': avg_buy_price,
                    'current_price': current_price,
                    'current_value': current_value,
                    'invested_value': invested_value,
                    'profit_loss': profit_loss,
                    'profit_loss_percent': profit_loss_percent
                }
                
                total_current_value += current_value
        
        return performance_data
    
    def create_advanced_alerts(self):
        """Crear sistema de alertas avanzadas"""
        alerts = []
        
        # Alertas de predicción
        items = self.data_manager.get_all_items()
        for item in items:
            prediction = self.predict_prices_ai(item, days_ahead=1)
            if prediction and prediction['confidence'] > 80:
                current_stats = self.data_manager.get_price_statistics(item)
                if current_stats:
                    predicted_change = ((prediction['predictions'][0] - current_stats['current_price']) / current_stats['current_price']) * 100
                    
                    if abs(predicted_change) > 15:
                        alerts.append({
                            'type': 'PREDICCIÓN_IA',
                            'item': item,
                            'message': f'IA predice cambio de {predicted_change:+.1f}% mañana',
                            'priority': 'ALTA' if abs(predicted_change) > 25 else 'MEDIA',
                            'confidence': prediction['confidence']
                        })
        
        # Alertas de patrones
        for item in items:
            patterns = self.detect_price_patterns(item)
            for pattern in patterns:
                if pattern['confidence'] > 85:
                    alerts.append({
                        'type': 'PATRÓN_TÉCNICO',
                        'item': item,
                        'message': pattern['message'],
                        'priority': 'ALTA',
                        'action': pattern['action']
                    })
        
        return alerts
    
    def generate_advanced_report(self):
        """Generar reporte avanzado con IA y análisis técnico"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'trading_signals': self.create_trading_signals(),
            'advanced_alerts': self.create_advanced_alerts(),
            'portfolio_performance': self.calculate_portfolio_performance(),
            'market_analysis': {}
        }
        
        # Análisis de mercado general
        items = self.data_manager.get_all_items()
        if items:
            all_prices = []
            for item in items:
                stats = self.data_manager.get_price_statistics(item)
                if stats:
                    all_prices.append(stats['current_price'])
            
            if all_prices:
                report['market_analysis'] = {
                    'total_items': len(items),
                    'avg_market_price': np.mean(all_prices),
                    'market_volatility': np.std(all_prices) / np.mean(all_prices) * 100,
                    'price_range': {'min': min(all_prices), 'max': max(all_prices)}
                }
        
        # Guardar reporte
        report_file = f"reports/advanced_report_{datetime.now().strftime('%Y%m%d_%H%M')}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report
    
    def create_notification_system(self):
        """Sistema de notificaciones push (Windows)"""
        try:
            import win10toast
            toaster = win10toast.ToastNotifier()
            
            def send_notification(title, message, duration=10):
                toaster.show_toast(title, message, duration=duration)
            
            return send_notification
        except ImportError:
            print("⚠️ Para notificaciones instala: pip install win10toast")
            return lambda title, message, duration=10: print(f"NOTIFICACIÓN: {title} - {message}")
    
    def run_advanced_monitoring(self):
        """Ejecutar monitoreo avanzado continuo"""
        notify = self.create_notification_system()
        
        print("🤖 MONITOREO AVANZADO INICIADO")
        print("=" * 50)
        
        while True:
            try:
                # Generar reporte avanzado
                report = self.generate_advanced_report()
                
                # Procesar alertas de alta prioridad
                high_priority_alerts = [alert for alert in report['advanced_alerts'] 
                                      if alert.get('priority') == 'ALTA']
                
                if high_priority_alerts:
                    for alert in high_priority_alerts:
                        notify(
                            f"🚨 ALERTA: {alert['item']}", 
                            alert['message']
                        )
                        print(f"🚨 {alert['item']}: {alert['message']}")
                
                # Procesar señales de trading
                strong_signals = [signal for signal in report['trading_signals'] 
                                if signal.get('confidence', 0) > 85]
                
                if strong_signals:
                    for signal in strong_signals:
                        notify(
                            f"📈 SEÑAL: {signal['item']}", 
                            f"{signal['signal']} - Confianza: {signal['confidence']:.0f}%"
                        )
                        print(f"📈 {signal['item']}: {signal['signal']} ({signal['confidence']:.0f}%)")
                
                print(f"✅ Monitoreo completado - {datetime.now().strftime('%H:%M:%S')}")
                
                # Esperar 30 minutos antes del próximo ciclo
                time.sleep(1800)
                
            except KeyboardInterrupt:
                print("\n⏹️ Monitoreo avanzado detenido")
                break
            except Exception as e:
                print(f"❌ Error en monitoreo: {e}")
                time.sleep(300)  # Esperar 5 minutos antes de reintentar

def main():
    """Función principal para automatización avanzada"""
    import sys
    
    automation = AdvancedAutomation()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'monitor':
            automation.run_advanced_monitoring()
        elif command == 'predict':
            item = sys.argv[2] if len(sys.argv) > 2 else None
            if item:
                result = automation.predict_prices_ai(item)
                if result:
                    print(f"📈 Predicción para {item}:")
                    for i, price in enumerate(result['predictions']):
                        print(f"   Día {i+1}: {price:.2f} (Confianza: {result['confidence']:.1f}%)")
        elif command == 'signals':
            signals = automation.create_trading_signals()
            print("📊 SEÑALES DE TRADING:")
            for signal in signals:
                print(f"   {signal['item']}: {signal['signal']} - {signal.get('reason', 'N/A')}")
        elif command == 'report':
            report = automation.generate_advanced_report()
            print("📋 Reporte avanzado generado")
            print(f"   Señales: {len(report['trading_signals'])}")
            print(f"   Alertas: {len(report['advanced_alerts'])}")
        else:
            print("Comandos disponibles:")
            print("  python advanced_automation.py monitor   - Monitoreo continuo")
            print("  python advanced_automation.py predict [item] - Predicción de precios")
            print("  python advanced_automation.py signals  - Señales de trading")
            print("  python advanced_automation.py report   - Reporte avanzado")
    else:
        print("🤖 AUTOMATIZACIÓN AVANZADA - Night Crows")
        print("1. Iniciar monitoreo avanzado")
        print("2. Generar predicciones IA")
        print("3. Ver señales de trading")
        print("4. Generar reporte avanzado")
        
        choice = input("\nSelecciona una opción (1-4): ")
        
        if choice == '1':
            automation.run_advanced_monitoring()
        elif choice == '2':
            items = automation.data_manager.get_all_items()
            if items:
                print("Objetos disponibles:", ", ".join(items))
                item = input("Ingresa el nombre del objeto: ")
                result = automation.predict_prices_ai(item)
                if result:
                    print(f"\n📈 Predicción para {item}:")
                    for i, price in enumerate(result['predictions']):
                        print(f"   Día {i+1}: {price:.2f}")
                    print(f"   Confianza: {result['confidence']:.1f}%")
        elif choice == '3':
            signals = automation.create_trading_signals()
            print("\n📊 SEÑALES DE TRADING:")
            for signal in signals:
                print(f"   {signal['item']}: {signal['signal']}")
        elif choice == '4':
            report = automation.generate_advanced_report()
            print("\n📋 Reporte avanzado generado exitosamente")

if __name__ == "__main__":
    main()
