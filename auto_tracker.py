#!/usr/bin/env python3
"""
Módulo de automatización para el rastreador de Night Crows
Incluye funciones para automatizar la recolección y análisis de datos
"""

import schedule
import time
import json
import os
from datetime import datetime, timedelta
import requests
from data_manager import DataManager
from chart_generator import ChartGenerator
import smtplib
try:
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    from email.mime.image import MimeImage
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False
    print("Funciones de email no disponibles")

class AutoTracker:
    def __init__(self, config_file="config/auto_config.json"):
        self.config_file = config_file
        self.data_manager = DataManager()
        self.chart_generator = ChartGenerator()
        self.config = self.load_config()
        
    def load_config(self):
        """Cargar configuración de automatización"""
        default_config = {
            "auto_alerts": {
                "enabled": True,
                "price_change_threshold": 10,  # Porcentaje de cambio para alerta
                "email_notifications": False,
                "email_config": {
                    "smtp_server": "smtp.gmail.com",
                    "smtp_port": 587,
                    "email": "",
                    "password": "",
                    "recipient": ""
                }
            },
            "auto_analysis": {
                "enabled": True,
                "daily_report": True,
                "weekly_summary": True
            },
            "data_import": {
                "enabled": False,
                "csv_file": "data/import.csv",
                "api_endpoint": ""
            },
            "watched_items": [
                "Espada de Hierro",
                "Armadura de Cuero",
                "Poción de Vida",
                "Gema Mágica"
            ]
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Combinar con configuración por defecto
                    for key in default_config:
                        if key not in config:
                            config[key] = default_config[key]
                    return config
            except:
                pass
        
        # Crear directorio y archivo de configuración
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        self.save_config(default_config)
        return default_config
    
    def save_config(self, config=None):
        """Guardar configuración"""
        if config is None:
            config = self.config
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    def import_from_csv(self, csv_file=None):
        """Importar datos desde archivo CSV"""
        import pandas as pd
        
        if csv_file is None:
            csv_file = self.config['data_import']['csv_file']
        
        if not os.path.exists(csv_file):
            print(f"❌ Archivo CSV no encontrado: {csv_file}")
            return False
        
        try:
            df = pd.read_csv(csv_file)
            required_columns = ['item_name', 'price', 'date']
            
            if not all(col in df.columns for col in required_columns):
                print(f"❌ El CSV debe tener las columnas: {required_columns}")
                return False
            
            imported_count = 0
            for _, row in df.iterrows():
                self.data_manager.add_item_data(
                    item_name=row['item_name'],
                    price=float(row['price']),
                    quantity=row.get('quantity', None),
                    server=row.get('server', None),
                    notes=row.get('notes', f"Importado desde CSV - {datetime.now().strftime('%Y-%m-%d')}")
                )
                imported_count += 1
            
            print(f"✅ Importados {imported_count} registros desde CSV")
            return True
            
        except Exception as e:
            print(f"❌ Error al importar CSV: {str(e)}")
            return False
    
    def create_csv_template(self, filename="data/template_import.csv"):
        """Crear plantilla CSV para importar datos"""
        import pandas as pd
        
        template_data = {
            'item_name': ['Espada de Hierro', 'Armadura de Cuero', 'Poción de Vida'],
            'price': [100.50, 75.25, 15.00],
            'quantity': [5, 3, 10],
            'server': ['Servidor 1', 'Servidor 2', 'Servidor 1'],
            'date': ['2024-01-15', '2024-01-15', '2024-01-15'],
            'notes': ['Precio matutino', 'Precio vespertino', 'Oferta especial']
        }
        
        df = pd.DataFrame(template_data)
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        df.to_csv(filename, index=False)
        print(f"✅ Plantilla CSV creada: {filename}")
    
    def check_price_alerts(self):
        """Verificar alertas de precios"""
        if not self.config['auto_alerts']['enabled']:
            return
        
        threshold = self.config['auto_alerts']['price_change_threshold']
        alerts = []
        
        for item_name in self.config['watched_items']:
            stats = self.data_manager.get_price_statistics(item_name)
            if not stats:
                continue
            
            current_price = stats['current_price']
            avg_price = stats['average_price']
            
            # Calcular cambio porcentual
            change_percent = ((current_price - avg_price) / avg_price) * 100
            
            if abs(change_percent) >= threshold:
                recommendation = self.data_manager.get_buy_sell_recommendation(item_name)
                alert = {
                    'item': item_name,
                    'current_price': current_price,
                    'average_price': avg_price,
                    'change_percent': change_percent,
                    'recommendation': recommendation,
                    'timestamp': datetime.now().isoformat()
                }
                alerts.append(alert)
        
        if alerts:
            self.process_alerts(alerts)
        
        return alerts
    
    def process_alerts(self, alerts):
        """Procesar alertas generadas"""
        print(f"\n🚨 ALERTAS DE PRECIOS ({len(alerts)} alertas)")
        print("=" * 50)
        
        for alert in alerts:
            print(f"📊 {alert['item']}")
            print(f"   Precio actual: {alert['current_price']:.2f}")
            print(f"   Precio promedio: {alert['average_price']:.2f}")
            print(f"   Cambio: {alert['change_percent']:+.1f}%")
            print(f"   🎯 {alert['recommendation']}")
            print()
        
        # Guardar alertas en archivo
        alerts_file = f"data/alerts_{datetime.now().strftime('%Y%m%d')}.json"
        os.makedirs(os.path.dirname(alerts_file), exist_ok=True)
        
        existing_alerts = []
        if os.path.exists(alerts_file):
            with open(alerts_file, 'r', encoding='utf-8') as f:
                existing_alerts = json.load(f)
        
        existing_alerts.extend(alerts)
        
        with open(alerts_file, 'w', encoding='utf-8') as f:
            json.dump(existing_alerts, f, indent=2, ensure_ascii=False)
        
        # Enviar email si está configurado
        if self.config['auto_alerts']['email_notifications']:
            self.send_email_alerts(alerts)
    
    def send_email_alerts(self, alerts):
        """Enviar alertas por email"""
        email_config = self.config['auto_alerts']['email_config']
        
        if not all([email_config['email'], email_config['password'], email_config['recipient']]):
            print("⚠️ Configuración de email incompleta")
            return
        
        try:
            msg = MimeMultipart()
            msg['From'] = email_config['email']
            msg['To'] = email_config['recipient']
            msg['Subject'] = f"Night Crows - Alertas de Precios ({len(alerts)} alertas)"
            
            # Crear contenido del email
            body = "🚨 ALERTAS DE PRECIOS - NIGHT CROWS\n\n"
            for alert in alerts:
                body += f"📊 {alert['item']}\n"
                body += f"   Precio actual: {alert['current_price']:.2f}\n"
                body += f"   Cambio: {alert['change_percent']:+.1f}%\n"
                body += f"   Recomendación: {alert['recommendation']}\n\n"
            
            msg.attach(MimeText(body, 'plain'))
            
            # Enviar email
            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            server.starttls()
            server.login(email_config['email'], email_config['password'])
            server.send_message(msg)
            server.quit()
            
            print("✅ Alertas enviadas por email")
            
        except Exception as e:
            print(f"❌ Error al enviar email: {str(e)}")
    
    def generate_daily_report(self):
        """Generar reporte diario automático"""
        if not self.config['auto_analysis']['daily_report']:
            return
        
        print(f"\n📋 REPORTE DIARIO - {datetime.now().strftime('%Y-%m-%d')}")
        print("=" * 50)
        
        report_data = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'items': {},
            'summary': {}
        }
        
        total_items = 0
        trending_up = 0
        trending_down = 0
        
        for item_name in self.data_manager.get_all_items():
            stats = self.data_manager.get_price_statistics(item_name)
            if stats:
                recommendation = self.data_manager.get_buy_sell_recommendation(item_name)
                
                print(f"📊 {item_name}")
                print(f"   Precio: {stats['current_price']:.2f} | Promedio: {stats['average_price']:.2f}")
                print(f"   Tendencia: {stats['price_trend']} | {recommendation}")
                print()
                
                report_data['items'][item_name] = {
                    'current_price': stats['current_price'],
                    'average_price': stats['average_price'],
                    'trend': stats['price_trend'],
                    'recommendation': recommendation
                }
                
                total_items += 1
                if stats['price_trend'] == 'Subiendo':
                    trending_up += 1
                elif stats['price_trend'] == 'Bajando':
                    trending_down += 1
        
        report_data['summary'] = {
            'total_items': total_items,
            'trending_up': trending_up,
            'trending_down': trending_down,
            'stable': total_items - trending_up - trending_down
        }
        
        print(f"📈 Resumen: {trending_up} subiendo, {trending_down} bajando, {total_items - trending_up - trending_down} estables")
        
        # Guardar reporte
        report_file = f"reports/daily_{datetime.now().strftime('%Y%m%d')}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        # Generar gráficos automáticos
        self.generate_auto_charts()
        
        return report_data
    
    def generate_auto_charts(self):
        """Generar gráficos automáticamente"""
        charts_dir = f"auto_charts/{datetime.now().strftime('%Y%m%d')}"
        os.makedirs(charts_dir, exist_ok=True)
        
        items = self.data_manager.get_all_items()
        
        # Gráfico de comparación general
        if len(items) >= 2:
            fig = self.chart_generator.create_comparison_chart(self.data_manager, items[:5], days=30)
            if fig:
                self.chart_generator.save_chart(fig, f"{charts_dir}/comparacion_general.png")
        
        # Gráficos individuales para items principales
        for item in items[:3]:  # Solo los primeros 3 para no saturar
            fig = self.chart_generator.create_price_chart(self.data_manager, item, days=30)
            if fig:
                safe_name = item.replace(' ', '_').replace('/', '_')
                self.chart_generator.save_chart(fig, f"{charts_dir}/{safe_name}_precios.png")
        
        print(f"✅ Gráficos automáticos generados en: {charts_dir}")
    
    def setup_scheduler(self):
        """Configurar tareas programadas"""
        print("⏰ Configurando tareas automáticas...")
        
        # Verificar alertas cada hora
        schedule.every().hour.do(self.check_price_alerts)
        
        # Reporte diario a las 8:00 AM
        schedule.every().day.at("08:00").do(self.generate_daily_report)
        
        # Importar datos automáticamente si hay archivo CSV
        if self.config['data_import']['enabled']:
            schedule.every().day.at("09:00").do(self.import_from_csv)
        
        print("✅ Tareas programadas configuradas:")
        print("   - Alertas de precios: cada hora")
        print("   - Reporte diario: 8:00 AM")
        if self.config['data_import']['enabled']:
            print("   - Importación automática: 9:00 AM")
    
    def run_scheduler(self):
        """Ejecutar el programador de tareas"""
        self.setup_scheduler()
        print("\n🤖 Modo automático iniciado. Presiona Ctrl+C para detener.")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Verificar cada minuto
        except KeyboardInterrupt:
            print("\n⏹️ Modo automático detenido.")
    
    def run_manual_check(self):
        """Ejecutar verificación manual de todas las funciones automáticas"""
        print("🔍 Ejecutando verificación manual...")
        
        # Verificar alertas
        alerts = self.check_price_alerts()
        
        # Generar reporte
        self.generate_daily_report()
        
        # Importar datos si hay archivo
        if self.config['data_import']['enabled'] and os.path.exists(self.config['data_import']['csv_file']):
            self.import_from_csv()
        
        print("✅ Verificación manual completada")
        return len(alerts) if alerts else 0

def main():
    """Función principal para ejecutar el tracker automático"""
    import sys
    
    tracker = AutoTracker()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'schedule':
            tracker.run_scheduler()
        elif command == 'check':
            tracker.run_manual_check()
        elif command == 'import':
            csv_file = sys.argv[2] if len(sys.argv) > 2 else None
            tracker.import_from_csv(csv_file)
        elif command == 'template':
            tracker.create_csv_template()
        elif command == 'report':
            tracker.generate_daily_report()
        else:
            print("Comandos disponibles:")
            print("  python auto_tracker.py schedule  - Ejecutar modo automático")
            print("  python auto_tracker.py check     - Verificación manual")
            print("  python auto_tracker.py import    - Importar desde CSV")
            print("  python auto_tracker.py template  - Crear plantilla CSV")
            print("  python auto_tracker.py report    - Generar reporte diario")
    else:
        # Modo interactivo
        print("🤖 Night Crows - Tracker Automático")
        print("1. Ejecutar verificación manual")
        print("2. Iniciar modo automático")
        print("3. Crear plantilla CSV")
        print("4. Importar datos desde CSV")
        print("5. Generar reporte diario")
        
        choice = input("\nSelecciona una opción (1-5): ")
        
        if choice == '1':
            tracker.run_manual_check()
        elif choice == '2':
            tracker.run_scheduler()
        elif choice == '3':
            tracker.create_csv_template()
        elif choice == '4':
            csv_file = input("Ruta del archivo CSV (Enter para usar por defecto): ").strip()
            tracker.import_from_csv(csv_file if csv_file else None)
        elif choice == '5':
            tracker.generate_daily_report()
        else:
            print("Opción no válida")

if __name__ == "__main__":
    main()
