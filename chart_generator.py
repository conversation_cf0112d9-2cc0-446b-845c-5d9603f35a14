import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import pandas as pd

class ChartGenerator:
    def __init__(self):
        # Configurar matplotlib para español
        plt.rcParams['font.size'] = 10
        plt.rcParams['figure.figsize'] = (12, 8)
        
    def create_price_chart(self, data_manager, item_name, days=30):
        """Crear gráfico de precios para un objeto"""
        df = data_manager.get_item_dataframe(item_name)
        
        if df.empty:
            print(f"No hay datos para {item_name}")
            return None
        
        # Filtrar por días recientes
        if days:
            cutoff_date = datetime.now() - timedelta(days=days)
            df = df[df['date'] >= cutoff_date]
        
        if df.empty:
            print(f"No hay datos recientes para {item_name}")
            return None
        
        # Crear el gráfico
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Gráfico de línea principal
        ax.plot(df['date'], df['price'], marker='o', linewidth=2, markersize=6, 
                color='#2E86AB', label='Precio')
        
        # Línea de promedio
        avg_price = df['price'].mean()
        ax.axhline(y=avg_price, color='#A23B72', linestyle='--', alpha=0.7, 
                   label=f'Promedio: {avg_price:.2f}')
        
        # Líneas de soporte y resistencia
        max_price = df['price'].max()
        min_price = df['price'].min()
        ax.axhline(y=max_price, color='#F18F01', linestyle=':', alpha=0.7, 
                   label=f'Máximo: {max_price:.2f}')
        ax.axhline(y=min_price, color='#C73E1D', linestyle=':', alpha=0.7, 
                   label=f'Mínimo: {min_price:.2f}')
        
        # Configurar el gráfico
        ax.set_title(f'Evolución de Precios - {item_name}', fontsize=16, fontweight='bold')
        ax.set_xlabel('Fecha', fontsize=12)
        ax.set_ylabel('Precio', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Formatear fechas en el eje X
        if len(df) > 0:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
            # Limitar el número de ticks para evitar sobrecarga
            max_ticks = min(10, len(df))
            interval = max(1, len(df) // max_ticks)
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=interval))
            plt.xticks(rotation=45)
        
        # Ajustar layout
        plt.tight_layout()
        
        return fig
    
    def create_comparison_chart(self, data_manager, item_names, days=30):
        """Crear gráfico comparativo de múltiples objetos"""
        fig, ax = plt.subplots(figsize=(14, 8))
        
        colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6A994E']
        
        for i, item_name in enumerate(item_names):
            df = data_manager.get_item_dataframe(item_name)
            
            if df.empty:
                continue
                
            # Filtrar por días recientes
            if days:
                cutoff_date = datetime.now() - timedelta(days=days)
                df = df[df['date'] >= cutoff_date]
            
            if df.empty:
                continue
            
            # Normalizar precios (porcentaje de cambio desde el primer valor)
            first_price = df['price'].iloc[0]
            normalized_prices = ((df['price'] - first_price) / first_price) * 100
            
            color = colors[i % len(colors)]
            ax.plot(df['date'], normalized_prices, marker='o', linewidth=2, 
                   markersize=4, color=color, label=item_name)
        
        ax.set_title('Comparación de Tendencias de Precios (% Cambio)', 
                    fontsize=16, fontweight='bold')
        ax.set_xlabel('Fecha', fontsize=12)
        ax.set_ylabel('Cambio Porcentual (%)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Formatear fechas
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        return fig
    
    def create_statistics_chart(self, data_manager, item_name):
        """Crear gráfico de estadísticas (barras)"""
        stats = data_manager.get_price_statistics(item_name)
        
        if not stats:
            print(f"No hay estadísticas para {item_name}")
            return None
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Gráfico de barras con estadísticas
        categories = ['Precio Actual', 'Promedio', 'Mínimo', 'Máximo']
        values = [stats['current_price'], stats['average_price'], 
                 stats['min_price'], stats['max_price']]
        colors = ['#2E86AB', '#A23B72', '#C73E1D', '#F18F01']
        
        bars = ax1.bar(categories, values, color=colors, alpha=0.8)
        ax1.set_title(f'Estadísticas de Precio - {item_name}', fontweight='bold')
        ax1.set_ylabel('Precio')
        
        # Agregar valores en las barras
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                    f'{value:.2f}', ha='center', va='bottom')
        
        # Gráfico de distribución de precios (histograma)
        df = data_manager.get_item_dataframe(item_name)
        if not df.empty:
            ax2.hist(df['price'], bins=min(20, len(df)), alpha=0.7, color='#6A994E')
            ax2.axvline(stats['average_price'], color='#A23B72', linestyle='--', 
                       label=f'Promedio: {stats["average_price"]:.2f}')
            ax2.set_title('Distribución de Precios')
            ax2.set_xlabel('Precio')
            ax2.set_ylabel('Frecuencia')
            ax2.legend()
        
        plt.tight_layout()
        return fig
    
    def save_chart(self, fig, filename):
        """Guardar gráfico como imagen"""
        if fig:
            fig.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"Gráfico guardado como {filename}")
    
    def show_chart(self, fig):
        """Mostrar gráfico"""
        if fig:
            plt.show()
        else:
            print("No se pudo generar el gráfico")
