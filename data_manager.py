import json
import os
from datetime import datetime
import pandas as pd

class DataManager:
    def __init__(self, data_file="data/night_crows_data.json"):
        self.data_file = data_file
        self.ensure_data_directory()
        self.data = self.load_data()
    
    def ensure_data_directory(self):
        """Crear directorio de datos si no existe"""
        os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
    
    def load_data(self):
        """Cargar datos desde archivo JSON"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}
    
    def save_data(self):
        """Guardar datos en archivo JSON"""
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, indent=2, ensure_ascii=False)
    
    def add_item_data(self, item_name, price, quantity=None, server=None, notes=""):
        """Agregar datos de un objeto"""
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        if item_name not in self.data:
            self.data[item_name] = []
        
        entry = {
            "date": current_date,
            "price": float(price),
            "quantity": quantity if quantity else None,
            "server": server if server else None,
            "notes": notes,
            "timestamp": datetime.now().isoformat()
        }
        
        self.data[item_name].append(entry)
        self.save_data()
    
    def get_item_data(self, item_name):
        """Obtener datos de un objeto específico"""
        return self.data.get(item_name, [])
    
    def get_all_items(self):
        """Obtener lista de todos los objetos registrados"""
        return list(self.data.keys())
    
    def get_item_dataframe(self, item_name):
        """Convertir datos de objeto a DataFrame de pandas"""
        item_data = self.get_item_data(item_name)
        if not item_data:
            return pd.DataFrame()
        
        df = pd.DataFrame(item_data)
        df['date'] = pd.to_datetime(df['date'])
        return df.sort_values('date')
    
    def get_price_statistics(self, item_name):
        """Obtener estadísticas de precio de un objeto"""
        df = self.get_item_dataframe(item_name)
        if df.empty:
            return None
        
        stats = {
            'current_price': df['price'].iloc[-1],
            'average_price': df['price'].mean(),
            'min_price': df['price'].min(),
            'max_price': df['price'].max(),
            'price_trend': self.calculate_trend(df['price']),
            'total_entries': len(df)
        }
        return stats
    
    def calculate_trend(self, prices):
        """Calcular tendencia de precios (subida, bajada, estable)"""
        if len(prices) < 2:
            return "Sin datos suficientes"
        
        recent_prices = prices.tail(5)  # Últimos 5 registros
        if len(recent_prices) < 2:
            return "Sin datos suficientes"
        
        trend = recent_prices.iloc[-1] - recent_prices.iloc[0]
        
        if trend > recent_prices.mean() * 0.1:  # Subida significativa
            return "Subiendo"
        elif trend < -recent_prices.mean() * 0.1:  # Bajada significativa
            return "Bajando"
        else:
            return "Estable"
    
    def get_buy_sell_recommendation(self, item_name):
        """Obtener recomendación de compra/venta"""
        stats = self.get_price_statistics(item_name)
        if not stats:
            return "Sin datos suficientes"
        
        current = stats['current_price']
        average = stats['average_price']
        trend = stats['price_trend']
        
        if current < average * 0.9 and trend == "Bajando":
            return "COMPRAR - Precio bajo y bajando"
        elif current < average * 0.95:
            return "Considerar COMPRAR - Precio por debajo del promedio"
        elif current > average * 1.1 and trend == "Subiendo":
            return "VENDER - Precio alto y subiendo"
        elif current > average * 1.05:
            return "Considerar VENDER - Precio por encima del promedio"
        else:
            return "MANTENER - Precio estable"
    
    def edit_item_entry(self, item_name, entry_index, new_price=None, new_quantity=None, new_server=None, new_notes=None):
        """Editar una entrada específica de un objeto"""
        if item_name not in self.data:
            return False, "El objeto no existe"
        
        if entry_index < 0 or entry_index >= len(self.data[item_name]):
            return False, "Índice de entrada inválido"
        
        entry = self.data[item_name][entry_index]
        
        # Actualizar campos si se proporcionan nuevos valores
        if new_price is not None:
            entry['price'] = float(new_price)
        if new_quantity is not None:
            entry['quantity'] = new_quantity
        if new_server is not None:
            entry['server'] = new_server
        if new_notes is not None:
            entry['notes'] = new_notes
        
        # Actualizar timestamp
        entry['timestamp'] = datetime.now().isoformat()
        
        self.save_data()
        return True, "Entrada actualizada correctamente"
    
    def delete_item_entry(self, item_name, entry_index):
        """Eliminar una entrada específica de un objeto"""
        if item_name not in self.data:
            return False, "El objeto no existe"
        
        if entry_index < 0 or entry_index >= len(self.data[item_name]):
            return False, "Índice de entrada inválido"
        
        # Eliminar la entrada
        deleted_entry = self.data[item_name].pop(entry_index)
        
        # Si no quedan entradas, eliminar el objeto completamente
        if not self.data[item_name]:
            del self.data[item_name]
        
        self.save_data()
        return True, f"Entrada eliminada: {deleted_entry['date']} - ${deleted_entry['price']:.2f}"
    
    def delete_item_completely(self, item_name):
        """Eliminar un objeto completamente con todas sus entradas"""
        if item_name not in self.data:
            return False, "El objeto no existe"
        
        entry_count = len(self.data[item_name])
        del self.data[item_name]
        
        self.save_data()
        return True, f"Objeto '{item_name}' eliminado completamente ({entry_count} entradas)"
    
    def rename_item(self, old_name, new_name):
        """Renombrar un objeto"""
        if old_name not in self.data:
            return False, "El objeto original no existe"
        
        if new_name in self.data:
            return False, "Ya existe un objeto con ese nombre"
        
        # Mover datos al nuevo nombre
        self.data[new_name] = self.data[old_name]
        del self.data[old_name]
        
        self.save_data()
        return True, f"Objeto renombrado de '{old_name}' a '{new_name}'"
    
    def get_item_entries_with_index(self, item_name):
        """Obtener entradas de un objeto con sus índices para edición"""
        if item_name not in self.data:
            return []
        
        entries_with_index = []
        for i, entry in enumerate(self.data[item_name]):
            entry_copy = entry.copy()
            entry_copy['index'] = i
            entries_with_index.append(entry_copy)
        
        return entries_with_index
    
    def search_items(self, search_term):
        """Buscar objetos por nombre"""
        search_term = search_term.lower()
        matching_items = []
        
        for item_name in self.data.keys():
            if search_term in item_name.lower():
                matching_items.append(item_name)
        
        return matching_items
    
    def get_item_summary(self, item_name):
        """Obtener resumen completo de un objeto"""
        if item_name not in self.data:
            return None
        
        entries = self.data[item_name]
        stats = self.get_price_statistics(item_name)
        recommendation = self.get_buy_sell_recommendation(item_name)
        
        return {
            'name': item_name,
            'total_entries': len(entries),
            'first_entry_date': entries[0]['date'] if entries else None,
            'last_entry_date': entries[-1]['date'] if entries else None,
            'statistics': stats,
            'recommendation': recommendation,
            'entries': entries
        }
    
    def backup_data(self, backup_filename=None):
        """Crear backup de todos los datos"""
        if backup_filename is None:
            backup_filename = f"backups/backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        os.makedirs(os.path.dirname(backup_filename), exist_ok=True)
        
        with open(backup_filename, 'w', encoding='utf-8') as f:
            json.dump(self.data, f, indent=2, ensure_ascii=False)
        
        return backup_filename
    
    def restore_data(self, backup_filename):
        """Restaurar datos desde backup"""
        if not os.path.exists(backup_filename):
            return False, "Archivo de backup no encontrado"
        
        try:
            with open(backup_filename, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # Crear backup actual antes de restaurar
            current_backup = self.backup_data()
            
            # Restaurar datos
            self.data = backup_data
            self.save_data()
            
            return True, f"Datos restaurados desde {backup_filename}. Backup actual guardado en {current_backup}"
        
        except Exception as e:
            return False, f"Error al restaurar backup: {str(e)}"
