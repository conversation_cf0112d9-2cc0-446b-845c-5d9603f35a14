#!/usr/bin/env python3
"""
Instalador Automático Completo para Night Crows Tracker
Instala y configura todo el sistema automáticamente
"""

import os
import sys
import subprocess
import json
import time
from datetime import datetime

class AutoInstaller:
    def __init__(self):
        self.steps_completed = 0
        self.total_steps = 12
        
    def print_step(self, step_name):
        """Imprimir paso actual"""
        self.steps_completed += 1
        print(f"\n[{self.steps_completed}/{self.total_steps}] {step_name}")
        print("=" * 60)
    
    def run_command(self, command, description=""):
        """Ejecutar comando con manejo de errores"""
        try:
            if description:
                print(f"🔄 {description}")
            
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Completado")
                return True
            else:
                print(f"❌ Error: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Excepción: {str(e)}")
            return False
    
    def install_dependencies(self):
        """Instalar todas las dependencias"""
        self.print_step("INSTALANDO DEPENDENCIAS")
        
        dependencies = [
            "matplotlib>=3.7.2",
            "pandas>=2.0.3", 
            "schedule>=1.2.0",
            "requests>=2.28.0",
            "scikit-learn>=1.3.0",
            "flask>=2.3.0",
            "plotly>=5.15.0",
            "win10toast>=0.9",
            "numpy>=1.24.0"
        ]
        
        print("📦 Instalando paquetes de Python...")
        for dep in dependencies:
            print(f"   Instalando {dep.split('>=')[0]}...")
            success = self.run_command(f"pip install {dep}")
            if not success:
                print(f"⚠️ Error instalando {dep}, continuando...")
        
        print("✅ Dependencias instaladas")
    
    def create_directories(self):
        """Crear estructura de directorios"""
        self.print_step("CREANDO ESTRUCTURA DE DIRECTORIOS")
        
        directories = [
            'config', 'data', 'reports', 'auto_charts', 'alerts',
            'templates', 'static', 'backups', 'logs', 'exports'
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            print(f"📁 {directory}/")
        
        print("✅ Directorios creados")
    
    def setup_sample_data(self):
        """Configurar datos de ejemplo"""
        self.print_step("CONFIGURANDO DATOS DE EJEMPLO")
        
        # Ejecutar setup_automation.py
        success = self.run_command("python setup_automation.py", "Ejecutando configuración automática")
        
        if success:
            print("✅ Datos de ejemplo configurados")
        else:
            print("⚠️ Error en configuración automática, continuando...")
    
    def create_batch_files(self):
        """Crear archivos .bat mejorados"""
        self.print_step("CREANDO ARCHIVOS DE EJECUCIÓN")
        
        batch_files = {
            'EJECUTAR_TRACKER.bat': '''@echo off
title Night Crows - Tracker Principal
echo ========================================
echo    NIGHT CROWS TRACKER PRINCIPAL
echo ========================================
echo.
echo Iniciando aplicacion principal...
python main.py
if errorlevel 1 (
    echo.
    echo ERROR: No se pudo iniciar la aplicacion
    echo Verifica que Python este instalado correctamente
    pause
)
''',
            
            'EJECUTAR_AUTOMATICO.bat': '''@echo off
title Night Crows - Modo Automatico
echo ========================================
echo    NIGHT CROWS MODO AUTOMATICO
echo ========================================
echo.
echo Iniciando modo automatico...
echo - Alertas cada hora
echo - Reportes diarios
echo - Importacion automatica
echo.
python auto_tracker.py schedule
if errorlevel 1 (
    echo.
    echo ERROR: No se pudo iniciar el modo automatico
    pause
)
''',
            
            'VERIFICAR_PRECIOS.bat': '''@echo off
title Night Crows - Verificacion Manual
echo ========================================
echo    VERIFICACION MANUAL DE PRECIOS
echo ========================================
echo.
python auto_tracker.py check
echo.
echo Verificacion completada.
pause
''',
            
            'DASHBOARD_WEB.bat': '''@echo off
title Night Crows - Dashboard Web
echo ========================================
echo    DASHBOARD WEB EN TIEMPO REAL
echo ========================================
echo.
echo Iniciando servidor web...
echo Dashboard disponible en: http://localhost:5000
echo.
python web_dashboard.py
if errorlevel 1 (
    echo.
    echo ERROR: No se pudo iniciar el dashboard web
    pause
)
''',
            
            'NOTIFICACIONES_MOVIL.bat': '''@echo off
title Night Crows - Notificaciones Movil
echo ========================================
echo    NOTIFICACIONES MOVILES
echo ========================================
echo.
echo Iniciando monitor de notificaciones...
python mobile_notifications.py monitor
if errorlevel 1 (
    echo.
    echo ERROR: No se pudo iniciar las notificaciones
    pause
)
''',
            
            'IA_PREDICCIONES.bat': '''@echo off
title Night Crows - Predicciones IA
echo ========================================
echo    PREDICCIONES CON INTELIGENCIA ARTIFICIAL
echo ========================================
echo.
python advanced_automation.py monitor
if errorlevel 1 (
    echo.
    echo ERROR: No se pudo iniciar las predicciones IA
    pause
)
''',
            
            'IMPORTAR_DATOS.bat': '''@echo off
title Night Crows - Importar Datos
echo ========================================
echo    IMPORTAR DATOS DESDE CSV
echo ========================================
echo.
echo Importando datos desde data/sample_data.csv...
python auto_tracker.py import
echo.
echo Importacion completada.
pause
''',
            
            'CONFIGURAR_TODO.bat': '''@echo off
title Night Crows - Configuracion Completa
echo ========================================
echo    CONFIGURACION COMPLETA DEL SISTEMA
echo ========================================
echo.
echo 1. Configurando notificaciones moviles...
python mobile_notifications.py setup
echo.
echo 2. Ejecutando verificacion completa...
python auto_tracker.py check
echo.
echo Configuracion completada.
pause
'''
        }
        
        for filename, content in batch_files.items():
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"📄 {filename}")
        
        print("✅ Archivos .bat creados")
    
    def create_desktop_shortcuts(self):
        """Crear accesos directos en el escritorio"""
        self.print_step("CREANDO ACCESOS DIRECTOS")
        
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            current_dir = os.getcwd()
            
            shortcuts = {
                'Night Crows Tracker.lnk': 'EJECUTAR_TRACKER.bat',
                'Night Crows Automatico.lnk': 'EJECUTAR_AUTOMATICO.bat',
                'Night Crows Dashboard.lnk': 'DASHBOARD_WEB.bat',
                'Night Crows Notificaciones.lnk': 'NOTIFICACIONES_MOVIL.bat'
            }
            
            shell = Dispatch('WScript.Shell')
            
            for shortcut_name, bat_file in shortcuts.items():
                shortcut_path = os.path.join(desktop, shortcut_name)
                shortcut = shell.CreateShortCut(shortcut_path)
                shortcut.Targetpath = os.path.join(current_dir, bat_file)
                shortcut.WorkingDirectory = current_dir
                shortcut.IconLocation = os.path.join(current_dir, bat_file)
                shortcut.save()
                print(f"🔗 {shortcut_name}")
            
            print("✅ Accesos directos creados en el escritorio")
            
        except ImportError:
            print("⚠️ No se pudieron crear accesos directos (winshell no disponible)")
            print("   Los archivos .bat están disponibles en la carpeta del proyecto")
    
    def create_startup_script(self):
        """Crear script de inicio automático"""
        self.print_step("CONFIGURANDO INICIO AUTOMÁTICO")
        
        startup_script = '''@echo off
REM Script de inicio automatico para Night Crows Tracker
REM Ejecuta el modo automatico al iniciar Windows

cd /d "%~dp0"

REM Esperar 30 segundos para que Windows termine de cargar
timeout /t 30 /nobreak >nul

REM Iniciar modo automatico en segundo plano
start /min "Night Crows Auto" EJECUTAR_AUTOMATICO.bat

REM Opcional: Iniciar dashboard web
REM start /min "Night Crows Web" DASHBOARD_WEB.bat

REM Opcional: Iniciar notificaciones moviles  
REM start /min "Night Crows Notifications" NOTIFICACIONES_MOVIL.bat
'''
        
        with open('INICIO_AUTOMATICO.bat', 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        print("📄 INICIO_AUTOMATICO.bat creado")
        print("   Para activar inicio automático:")
        print("   1. Presiona Win+R, escribe 'shell:startup'")
        print("   2. Copia INICIO_AUTOMATICO.bat a esa carpeta")
        print("✅ Script de inicio creado")
    
    def create_configuration_files(self):
        """Crear archivos de configuración avanzada"""
        self.print_step("CREANDO CONFIGURACIONES AVANZADAS")
        
        # Configuración principal
        main_config = {
            "version": "2.0.0",
            "installation_date": datetime.now().isoformat(),
            "features": {
                "basic_tracking": True,
                "advanced_automation": True,
                "ai_predictions": True,
                "web_dashboard": True,
                "mobile_notifications": True,
                "auto_import": True,
                "portfolio_tracking": True
            },
            "paths": {
                "data_directory": "data/",
                "reports_directory": "reports/",
                "charts_directory": "auto_charts/",
                "backups_directory": "backups/"
            }
        }
        
        with open('config/main_config.json', 'w', encoding='utf-8') as f:
            json.dump(main_config, f, indent=2, ensure_ascii=False)
        
        # Configuración de trading
        trading_config = {
            "risk_management": {
                "max_investment_per_item": 1000,
                "stop_loss_percentage": 15,
                "take_profit_percentage": 25,
                "max_daily_trades": 5
            },
            "trading_signals": {
                "min_confidence": 75,
                "use_ai_predictions": True,
                "use_technical_analysis": True,
                "follow_trends": True
            },
            "portfolio": {
                "auto_rebalance": False,
                "target_diversification": 5,
                "cash_reserve_percentage": 20
            }
        }
        
        with open('config/trading_config.json', 'w', encoding='utf-8') as f:
            json.dump(trading_config, f, indent=2, ensure_ascii=False)
        
        print("✅ Archivos de configuración creados")
    
    def create_documentation(self):
        """Crear documentación completa"""
        self.print_step("GENERANDO DOCUMENTACIÓN")
        
        readme_content = '''# 🚀 NIGHT CROWS TRACKER - SISTEMA COMPLETO

## 🎯 INICIO RÁPIDO (1 CLICK)

### Para Usuarios Básicos:
- **EJECUTAR_TRACKER.bat** - Aplicación principal con interfaz gráfica
- **VERIFICAR_PRECIOS.bat** - Verificación rápida de precios

### Para Usuarios Avanzados:
- **EJECUTAR_AUTOMATICO.bat** - Modo automático completo
- **DASHBOARD_WEB.bat** - Dashboard web en tiempo real
- **NOTIFICACIONES_MOVIL.bat** - Notificaciones en tu móvil
- **IA_PREDICCIONES.bat** - Predicciones con Inteligencia Artificial

## 🤖 FUNCIONES AUTOMÁTICAS

### ✅ Básicas (Siempre Activas)
- Rastreo diario de precios
- Gráficos automáticos
- Recomendaciones de compra/venta
- Almacenamiento seguro de datos

### ✅ Avanzadas (Configurables)
- **Inteligencia Artificial**: Predicciones de precios con ML
- **Alertas Inteligentes**: Notificaciones cuando hay oportunidades
- **Dashboard Web**: Interfaz moderna accesible desde cualquier dispositivo
- **Notificaciones Móviles**: Telegram, Discord, Push notifications
- **Análisis Técnico**: Detección de patrones y tendencias
- **Importación Masiva**: Procesa cientos de registros desde CSV
- **Portafolio Personal**: Rastrea tus inversiones y ganancias

## 📱 CONFIGURAR NOTIFICACIONES MÓVILES

### Opción 1: ntfy.sh (Más Fácil)
1. Instala la app "ntfy" desde Google Play o App Store
2. Ejecuta: `CONFIGURAR_TODO.bat`
3. Elige un nombre único para tu tema (ej: nightcrows_juan123)
4. Suscríbete al tema en la app

### Opción 2: Telegram
1. Habla con @BotFather en Telegram
2. Crea un bot con `/newbot`
3. Habla con @userinfobot para obtener tu chat_id
4. Ejecuta: `CONFIGURAR_TODO.bat`

## 🌐 DASHBOARD WEB

Ejecuta `DASHBOARD_WEB.bat` y abre: http://localhost:5000

**Características:**
- Gráficos interactivos en tiempo real
- Alertas y señales automáticas
- Compatible con móviles y tablets
- API REST para integraciones
- Agregar precios desde la web

## 🧠 INTELIGENCIA ARTIFICIAL

El sistema incluye:
- **Predicciones de precios** usando Machine Learning
- **Detección de patrones** técnicos automática
- **Señales de trading** con niveles de confianza
- **Análisis de tendencias** avanzado

## 📊 IMPORTAR DATOS MASIVAMENTE

1. Edita `data/sample_data.csv` con tus datos
2. Formato: `item_name,price,quantity,server,date,notes`
3. Ejecuta: `IMPORTAR_DATOS.bat`

## ⚙️ CONFIGURACIÓN AVANZADA

### Archivos de Configuración:
- `config/main_config.json` - Configuración principal
- `config/auto_config.json` - Automatización
- `config/notifications_config.json` - Notificaciones
- `config/trading_config.json` - Trading y portafolio

### Personalizar Alertas:
Edita `config/auto_config.json`:
```json
{
  "auto_alerts": {
    "price_change_threshold": 15,
    "watched_items": ["Tu Objeto Favorito"]
  }
}
```

## 🔄 INICIO AUTOMÁTICO

Para que el sistema funcione automáticamente al encender la PC:
1. Presiona Win+R, escribe `shell:startup`
2. Copia `INICIO_AUTOMATICO.bat` a esa carpeta
3. ¡Listo! El sistema se iniciará automáticamente

## 📈 CASOS DE USO

### Trader Casual:
- Usa `EJECUTAR_TRACKER.bat` para ingresar precios manualmente
- Revisa recomendaciones diarias

### Trader Activo:
- Ejecuta `EJECUTAR_AUTOMATICO.bat` en segundo plano
- Configura notificaciones móviles
- Usa el dashboard web para monitoreo

### Trader Profesional:
- Activa todas las funciones automáticas
- Usa predicciones IA para decisiones
- Configura inicio automático
- Importa datos históricos masivamente

## 🆘 SOLUCIÓN DE PROBLEMAS

### Error: "No se reconoce python"
- Instala Python desde python.org
- Marca "Add Python to PATH" durante instalación

### Error: "Módulo no encontrado"
- Ejecuta: `pip install -r requirements.txt`

### Dashboard web no carga:
- Verifica que el puerto 5000 esté libre
- Ejecuta como administrador si es necesario

### Notificaciones no llegan:
- Ejecuta `CONFIGURAR_TODO.bat` para reconfigurar
- Verifica conexión a internet

## 📞 SOPORTE

El sistema es completamente automático y no requiere mantenimiento.
Todos los datos se guardan localmente en tu computadora.

¡Disfruta del trading automático! 🚀
'''
        
        with open('LEEME.md', 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✅ Documentación generada: LEEME.md")
    
    def run_initial_tests(self):
        """Ejecutar pruebas iniciales"""
        self.print_step("EJECUTANDO PRUEBAS INICIALES")
        
        print("🧪 Probando componentes básicos...")
        
        # Probar importación de módulos
        modules_to_test = [
            'data_manager', 'chart_generator', 'auto_tracker',
            'advanced_automation', 'mobile_notifications'
        ]
        
        for module in modules_to_test:
            try:
                __import__(module)
                print(f"✅ {module}")
            except ImportError as e:
                print(f"❌ {module}: {e}")
        
        # Probar creación de datos de ejemplo
        success = self.run_command("python auto_tracker.py check", "Verificando sistema automático")
        
        if success:
            print("✅ Sistema funcionando correctamente")
        else:
            print("⚠️ Algunos componentes pueden necesitar configuración adicional")
    
    def create_uninstaller(self):
        """Crear desinstalador"""
        self.print_step("CREANDO DESINSTALADOR")
        
        uninstaller_content = '''@echo off
title Night Crows Tracker - Desinstalador
echo ========================================
echo    DESINSTALADOR NIGHT CROWS TRACKER
echo ========================================
echo.
echo ADVERTENCIA: Esto eliminara todos los datos y configuraciones
echo.
set /p confirm="¿Estas seguro? (s/N): "
if /i not "%confirm%"=="s" (
    echo Desinstalacion cancelada.
    pause
    exit /b
)

echo.
echo Eliminando archivos...

REM Eliminar accesos directos del escritorio
del "%USERPROFILE%\\Desktop\\Night Crows*.lnk" 2>nul

REM Eliminar directorios de datos (opcional)
echo.
set /p deletedata="¿Eliminar tambien los datos guardados? (s/N): "
if /i "%deletedata%"=="s" (
    rmdir /s /q data 2>nul
    rmdir /s /q reports 2>nul
    rmdir /s /q auto_charts 2>nul
    rmdir /s /q config 2>nul
    echo Datos eliminados.
)

REM Eliminar archivos del sistema
del *.bat 2>nul
del *.py 2>nul
del *.md 2>nul
del requirements.txt 2>nul

echo.
echo ========================================
echo    DESINSTALACION COMPLETADA
echo ========================================
echo.
echo El Night Crows Tracker ha sido eliminado.
echo Gracias por usar nuestro sistema!
echo.
pause
'''
        
        with open('DESINSTALAR.bat', 'w', encoding='utf-8') as f:
            f.write(uninstaller_content)
        
        print("✅ Desinstalador creado: DESINSTALAR.bat")
    
    def finalize_installation(self):
        """Finalizar instalación"""
        self.print_step("FINALIZANDO INSTALACIÓN")
        
        print("🎉 INSTALACIÓN COMPLETADA EXITOSAMENTE!")
        print("=" * 50)
        print()
        print("📋 RESUMEN DE INSTALACIÓN:")
        print(f"   ✅ {self.total_steps} pasos completados")
        print("   ✅ Todas las dependencias instaladas")
        print("   ✅ Datos de ejemplo configurados")
        print("   ✅ Archivos de ejecución creados")
        print("   ✅ Documentación generada")
        print()
        print("🚀 PRÓXIMOS PASOS:")
        print("   1. Ejecuta: EJECUTAR_TRACKER.bat (aplicación básica)")
        print("   2. O ejecuta: EJECUTAR_AUTOMATICO.bat (modo automático)")
        print("   3. Lee: LEEME.md (guía completa)")
        print()
        print("📱 CONFIGURAR NOTIFICACIONES:")
        print("   Ejecuta: CONFIGURAR_TODO.bat")
        print()
        print("🌐 DASHBOARD WEB:")
        print("   Ejecuta: DASHBOARD_WEB.bat")
        print("   Luego abre: http://localhost:5000")
        print()
        print("🤖 MODO AUTOMÁTICO COMPLETO:")
        print("   - Alertas inteligentes cada hora")
        print("   - Reportes diarios automáticos")
        print("   - Predicciones con IA")
        print("   - Notificaciones móviles")
        print()
        print("¡El sistema está listo para usar! 🎯")

def main():
    """Función principal del instalador"""
    print("🚀 INSTALADOR AUTOMÁTICO - NIGHT CROWS TRACKER")
    print("=" * 60)
    print("Este instalador configurará automáticamente:")
    print("✅ Todas las dependencias de Python")
    print("✅ Estructura de directorios")
    print("✅ Datos de ejemplo")
    print("✅ Archivos de ejecución (.bat)")
    print("✅ Configuraciones avanzadas")
    print("✅ Documentación completa")
    print("✅ Pruebas del sistema")
    print()
    
    response = input("¿Continuar con la instalación? (S/n): ").lower()
    if response in ['n', 'no']:
        print("Instalación cancelada.")
        return
    
    installer = AutoInstaller()
    
    try:
        installer.install_dependencies()
        installer.create_directories()
        installer.setup_sample_data()
        installer.create_batch_files()
        installer.create_desktop_shortcuts()
        installer.create_startup_script()
        installer.create_configuration_files()
        installer.create_documentation()
        installer.run_initial_tests()
        installer.create_uninstaller()
        installer.finalize_installation()
        
    except KeyboardInterrupt:
        print("\n⏹️ Instalación interrumpida por el usuario")
    except Exception as e:
        print(f"\n❌ Error durante la instalación: {e}")
        print("Puedes intentar ejecutar los pasos manualmente")

if __name__ == "__main__":
    main()
