import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading

from data_manager import DataManager
from chart_generator import ChartGenerator

class NightCrowsTracker:
    def __init__(self, root):
        self.root = root
        self.root.title("Night Crows - Rastreador de Precios")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2C3E50')
        
        # Inicializar componentes
        self.data_manager = DataManager()
        self.chart_generator = ChartGenerator()
        
        # Variables
        self.selected_item = tk.StringVar()
        self.current_chart = None
        
        self.setup_ui()
        self.refresh_item_list()
    
    def setup_ui(self):
        """Configurar la interfaz de usuario"""
        # Estilo
        style = ttk.Style()
        style.theme_use('clam')
        
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurar grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Panel izquierdo - Controles
        self.setup_control_panel(main_frame)
        
        # Panel derecho - Gráficos
        self.setup_chart_panel(main_frame)
    
    def setup_control_panel(self, parent):
        """Configurar panel de controles"""
        control_frame = ttk.LabelFrame(parent, text="Controles", padding="10")
        control_frame.grid(row=0, column=0, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Sección: Agregar datos
        ttk.Label(control_frame, text="Agregar Nuevo Registro", font=('Arial', 12, 'bold')).grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # Nombre del objeto
        ttk.Label(control_frame, text="Nombre del Objeto:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.item_name_entry = ttk.Entry(control_frame, width=25)
        self.item_name_entry.grid(row=1, column=1, pady=2, padx=(5, 0))
        
        # Precio
        ttk.Label(control_frame, text="Precio:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.price_entry = ttk.Entry(control_frame, width=25)
        self.price_entry.grid(row=2, column=1, pady=2, padx=(5, 0))
        
        # Cantidad (opcional)
        ttk.Label(control_frame, text="Cantidad (opcional):").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.quantity_entry = ttk.Entry(control_frame, width=25)
        self.quantity_entry.grid(row=3, column=1, pady=2, padx=(5, 0))
        
        # Servidor (opcional)
        ttk.Label(control_frame, text="Servidor (opcional):").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.server_entry = ttk.Entry(control_frame, width=25)
        self.server_entry.grid(row=4, column=1, pady=2, padx=(5, 0))
        
        # Notas (opcional)
        ttk.Label(control_frame, text="Notas (opcional):").grid(row=5, column=0, sticky=tk.W, pady=2)
        self.notes_entry = ttk.Entry(control_frame, width=25)
        self.notes_entry.grid(row=5, column=1, pady=2, padx=(5, 0))
        
        # Botón agregar
        ttk.Button(control_frame, text="Agregar Registro", command=self.add_record).grid(row=6, column=0, columnspan=2, pady=10)
        
        # Separador
        ttk.Separator(control_frame, orient='horizontal').grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # Sección: Visualización
        ttk.Label(control_frame, text="Visualización", font=('Arial', 12, 'bold')).grid(row=8, column=0, columnspan=2, pady=(0, 10))
        
        # Lista de objetos
        ttk.Label(control_frame, text="Seleccionar Objeto:").grid(row=9, column=0, sticky=tk.W, pady=2)
        self.item_combobox = ttk.Combobox(control_frame, textvariable=self.selected_item, width=22, state="readonly")
        self.item_combobox.grid(row=9, column=1, pady=2, padx=(5, 0))
        
        # Días a mostrar
        ttk.Label(control_frame, text="Días a mostrar:").grid(row=10, column=0, sticky=tk.W, pady=2)
        self.days_var = tk.StringVar(value="30")
        days_spinbox = ttk.Spinbox(control_frame, from_=7, to=365, textvariable=self.days_var, width=23)
        days_spinbox.grid(row=10, column=1, pady=2, padx=(5, 0))
        
        # Botones de gráficos
        ttk.Button(control_frame, text="Gráfico de Precios", command=self.show_price_chart).grid(row=11, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(control_frame, text="Estadísticas", command=self.show_statistics_chart).grid(row=12, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(control_frame, text="Comparar Objetos", command=self.show_comparison_dialog).grid(row=13, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))
        
        # Separador
        ttk.Separator(control_frame, orient='horizontal').grid(row=14, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # Sección: Gestión de datos
        ttk.Label(control_frame, text="Gestión de Datos", font=('Arial', 12, 'bold')).grid(row=15, column=0, columnspan=2, pady=(0, 10))
        
        # Botones de gestión
        ttk.Button(control_frame, text="Editar Registros", command=self.show_edit_dialog).grid(row=16, column=0, columnspan=2, pady=2, sticky=(tk.W, tk.E))
        ttk.Button(control_frame, text="Eliminar Objeto", command=self.show_delete_dialog).grid(row=17, column=0, columnspan=2, pady=2, sticky=(tk.W, tk.E))
        ttk.Button(control_frame, text="Renombrar Objeto", command=self.show_rename_dialog).grid(row=18, column=0, columnspan=2, pady=2, sticky=(tk.W, tk.E))
        ttk.Button(control_frame, text="Crear Backup", command=self.create_backup).grid(row=19, column=0, columnspan=2, pady=2, sticky=(tk.W, tk.E))
        
        # Separador
        ttk.Separator(control_frame, orient='horizontal').grid(row=20, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        # Información del objeto seleccionado
        self.info_frame = ttk.LabelFrame(control_frame, text="Información del Objeto", padding="5")
        self.info_frame.grid(row=21, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.info_text = tk.Text(self.info_frame, height=8, width=35, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(self.info_frame, orient="vertical", command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Actualizar información cuando cambie la selección
        self.item_combobox.bind('<<ComboboxSelected>>', self.update_item_info)
    
    def setup_chart_panel(self, parent):
        """Configurar panel de gráficos"""
        self.chart_frame = ttk.LabelFrame(parent, text="Gráficos", padding="10")
        self.chart_frame.grid(row=0, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Frame para el canvas del gráfico
        self.canvas_frame = ttk.Frame(self.chart_frame)
        self.canvas_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.chart_frame.columnconfigure(0, weight=1)
        self.chart_frame.rowconfigure(0, weight=1)
        
        # Mensaje inicial
        self.welcome_label = ttk.Label(self.canvas_frame, 
                                     text="Bienvenido al Rastreador de Precios de Night Crows\n\n" +
                                          "1. Agrega registros diarios de precios\n" +
                                          "2. Selecciona un objeto para ver gráficos\n" +
                                          "3. Analiza las tendencias para decidir cuándo comprar/vender",
                                     font=('Arial', 12),
                                     justify=tk.CENTER)
        self.welcome_label.place(relx=0.5, rely=0.5, anchor=tk.CENTER)
    
    def add_record(self):
        """Agregar nuevo registro"""
        try:
            item_name = self.item_name_entry.get().strip()
            price_str = self.price_entry.get().strip()
            
            if not item_name or not price_str:
                messagebox.showerror("Error", "Nombre del objeto y precio son obligatorios")
                return
            
            price = float(price_str)
            quantity = self.quantity_entry.get().strip() or None
            server = self.server_entry.get().strip() or None
            notes = self.notes_entry.get().strip() or ""
            
            if quantity:
                quantity = int(quantity)
            
            self.data_manager.add_item_data(item_name, price, quantity, server, notes)
            
            # Limpiar campos
            self.item_name_entry.delete(0, tk.END)
            self.price_entry.delete(0, tk.END)
            self.quantity_entry.delete(0, tk.END)
            self.server_entry.delete(0, tk.END)
            self.notes_entry.delete(0, tk.END)
            
            # Actualizar lista
            self.refresh_item_list()
            
            messagebox.showinfo("Éxito", f"Registro agregado para {item_name}")
            
        except ValueError:
            messagebox.showerror("Error", "Precio y cantidad deben ser números válidos")
        except Exception as e:
            messagebox.showerror("Error", f"Error al agregar registro: {str(e)}")
    
    def refresh_item_list(self):
        """Actualizar lista de objetos"""
        items = self.data_manager.get_all_items()
        self.item_combobox['values'] = items
        if items and not self.selected_item.get():
            self.selected_item.set(items[0])
            self.update_item_info()
    
    def update_item_info(self, event=None):
        """Actualizar información del objeto seleccionado"""
        item_name = self.selected_item.get()
        if not item_name:
            return
        
        stats = self.data_manager.get_price_statistics(item_name)
        recommendation = self.data_manager.get_buy_sell_recommendation(item_name)
        
        if stats:
            info_text = f"ESTADÍSTICAS DE {item_name.upper()}\n"
            info_text += "=" * 40 + "\n\n"
            info_text += f"Precio Actual: {stats['current_price']:.2f}\n"
            info_text += f"Precio Promedio: {stats['average_price']:.2f}\n"
            info_text += f"Precio Mínimo: {stats['min_price']:.2f}\n"
            info_text += f"Precio Máximo: {stats['max_price']:.2f}\n"
            info_text += f"Tendencia: {stats['price_trend']}\n"
            info_text += f"Total de registros: {stats['total_entries']}\n\n"
            info_text += "RECOMENDACIÓN:\n"
            info_text += f"{recommendation}\n\n"
            
            # Últimos registros
            recent_data = self.data_manager.get_item_data(item_name)[-5:]
            info_text += "ÚLTIMOS REGISTROS:\n"
            for record in reversed(recent_data):
                info_text += f"{record['date']}: {record['price']:.2f}\n"
        else:
            info_text = "No hay datos para este objeto"
        
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, info_text)
    
    def show_price_chart(self):
        """Mostrar gráfico de precios"""
        item_name = self.selected_item.get()
        if not item_name:
            messagebox.showwarning("Advertencia", "Selecciona un objeto primero")
            return
        
        try:
            days = int(self.days_var.get())
            self.clear_chart()
            
            # Crear gráfico en un hilo separado para no bloquear la UI
            def create_chart():
                fig = self.chart_generator.create_price_chart(self.data_manager, item_name, days)
                if fig:
                    self.root.after(0, lambda: self.display_chart(fig))
                else:
                    self.root.after(0, lambda: messagebox.showinfo("Info", "No hay datos suficientes para generar el gráfico"))
            
            threading.Thread(target=create_chart, daemon=True).start()
            
        except ValueError:
            messagebox.showerror("Error", "Número de días debe ser un valor válido")
    
    def show_statistics_chart(self):
        """Mostrar gráfico de estadísticas"""
        item_name = self.selected_item.get()
        if not item_name:
            messagebox.showwarning("Advertencia", "Selecciona un objeto primero")
            return
        
        self.clear_chart()
        
        def create_chart():
            fig = self.chart_generator.create_statistics_chart(self.data_manager, item_name)
            if fig:
                self.root.after(0, lambda: self.display_chart(fig))
            else:
                self.root.after(0, lambda: messagebox.showinfo("Info", "No hay datos suficientes para generar estadísticas"))
        
        threading.Thread(target=create_chart, daemon=True).start()
    
    def show_comparison_dialog(self):
        """Mostrar diálogo para comparar objetos"""
        items = self.data_manager.get_all_items()
        if len(items) < 2:
            messagebox.showinfo("Info", "Necesitas al menos 2 objetos para hacer comparaciones")
            return
        
        # Crear ventana de selección
        dialog = tk.Toplevel(self.root)
        dialog.title("Seleccionar Objetos para Comparar")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="Selecciona los objetos a comparar:", font=('Arial', 12)).pack(pady=10)
        
        # Lista de checkboxes
        frame = ttk.Frame(dialog)
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        selected_items = {}
        for item in items:
            var = tk.BooleanVar()
            ttk.Checkbutton(frame, text=item, variable=var).pack(anchor=tk.W, pady=2)
            selected_items[item] = var
        
        def compare_selected():
            selected = [item for item, var in selected_items.items() if var.get()]
            if len(selected) < 2:
                messagebox.showwarning("Advertencia", "Selecciona al menos 2 objetos")
                return
            
            dialog.destroy()
            self.show_comparison_chart(selected)
        
        ttk.Button(dialog, text="Comparar", command=compare_selected).pack(pady=10)
    
    def show_comparison_chart(self, item_names):
        """Mostrar gráfico de comparación"""
        try:
            days = int(self.days_var.get())
            self.clear_chart()
            
            def create_chart():
                fig = self.chart_generator.create_comparison_chart(self.data_manager, item_names, days)
                if fig:
                    self.root.after(0, lambda: self.display_chart(fig))
                else:
                    self.root.after(0, lambda: messagebox.showinfo("Info", "No hay datos suficientes para la comparación"))
            
            threading.Thread(target=create_chart, daemon=True).start()
            
        except ValueError:
            messagebox.showerror("Error", "Número de días debe ser un valor válido")
    
    def display_chart(self, fig):
        """Mostrar gráfico en la interfaz"""
        self.clear_chart()
        
        canvas = FigureCanvasTkAgg(fig, self.canvas_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        self.current_chart = canvas
        
        # Ocultar mensaje de bienvenida
        self.welcome_label.place_forget()
    
    def clear_chart(self):
        """Limpiar gráfico actual"""
        if self.current_chart:
            self.current_chart.get_tk_widget().destroy()
            self.current_chart = None
        
        # Limpiar cualquier widget en canvas_frame
        for widget in self.canvas_frame.winfo_children():
            widget.destroy()
    
    def show_edit_dialog(self):
        """Mostrar diálogo para editar registros"""
        item_name = self.selected_item.get()
        if not item_name:
            messagebox.showwarning("Advertencia", "Selecciona un objeto primero")
            return
        
        entries = self.data_manager.get_item_entries_with_index(item_name)
        if not entries:
            messagebox.showinfo("Info", "No hay registros para editar")
            return
        
        # Crear ventana de edición
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Editar Registros - {item_name}")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Frame principal con scrollbar
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Crear Treeview para mostrar registros
        columns = ('Fecha', 'Precio', 'Cantidad', 'Servidor', 'Notas')
        tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=10)
        
        # Configurar columnas
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=100)
        
        # Agregar datos
        for entry in entries:
            values = (
                entry['date'],
                f"{entry['price']:.2f}",
                entry.get('quantity', ''),
                entry.get('server', ''),
                entry.get('notes', '')
            )
            tree.insert('', tk.END, values=values, tags=(entry['index'],))
        
        tree.pack(fill=tk.BOTH, expand=True)
        
        # Frame para botones
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        def edit_selected():
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("Advertencia", "Selecciona un registro para editar")
                return
            
            item = tree.item(selection[0])
            entry_index = int(item['tags'][0])
            current_values = item['values']
            
            # Crear diálogo de edición
            edit_dialog = tk.Toplevel(dialog)
            edit_dialog.title("Editar Registro")
            edit_dialog.geometry("300x250")
            edit_dialog.transient(dialog)
            edit_dialog.grab_set()
            
            # Campos de edición
            ttk.Label(edit_dialog, text="Precio:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
            price_var = tk.StringVar(value=current_values[1])
            price_entry = ttk.Entry(edit_dialog, textvariable=price_var)
            price_entry.grid(row=0, column=1, padx=5, pady=5)
            
            ttk.Label(edit_dialog, text="Cantidad:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
            quantity_var = tk.StringVar(value=current_values[2])
            quantity_entry = ttk.Entry(edit_dialog, textvariable=quantity_var)
            quantity_entry.grid(row=1, column=1, padx=5, pady=5)
            
            ttk.Label(edit_dialog, text="Servidor:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
            server_var = tk.StringVar(value=current_values[3])
            server_entry = ttk.Entry(edit_dialog, textvariable=server_var)
            server_entry.grid(row=2, column=1, padx=5, pady=5)
            
            ttk.Label(edit_dialog, text="Notas:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
            notes_var = tk.StringVar(value=current_values[4])
            notes_entry = ttk.Entry(edit_dialog, textvariable=notes_var)
            notes_entry.grid(row=3, column=1, padx=5, pady=5)
            
            def save_changes():
                try:
                    new_price = float(price_var.get()) if price_var.get() else None
                    new_quantity = int(quantity_var.get()) if quantity_var.get() else None
                    new_server = server_var.get() if server_var.get() else None
                    new_notes = notes_var.get() if notes_var.get() else None
                    
                    success, message = self.data_manager.edit_item_entry(
                        item_name, entry_index, new_price, new_quantity, new_server, new_notes
                    )
                    
                    if success:
                        messagebox.showinfo("Éxito", message)
                        edit_dialog.destroy()
                        dialog.destroy()
                        self.refresh_item_list()
                        self.update_item_info()
                    else:
                        messagebox.showerror("Error", message)
                        
                except ValueError:
                    messagebox.showerror("Error", "Precio y cantidad deben ser números válidos")
            
            ttk.Button(edit_dialog, text="Guardar", command=save_changes).grid(row=4, column=0, columnspan=2, pady=10)
        
        def delete_selected():
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("Advertencia", "Selecciona un registro para eliminar")
                return
            
            if messagebox.askyesno("Confirmar", "¿Estás seguro de eliminar este registro?"):
                item = tree.item(selection[0])
                entry_index = int(item['tags'][0])
                
                success, message = self.data_manager.delete_item_entry(item_name, entry_index)
                
                if success:
                    messagebox.showinfo("Éxito", message)
                    dialog.destroy()
                    self.refresh_item_list()
                    self.update_item_info()
                else:
                    messagebox.showerror("Error", message)
        
        ttk.Button(button_frame, text="Editar Seleccionado", command=edit_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Eliminar Seleccionado", command=delete_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cerrar", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)
    
    def show_delete_dialog(self):
        """Mostrar diálogo para eliminar objetos"""
        items = self.data_manager.get_all_items()
        if not items:
            messagebox.showinfo("Info", "No hay objetos para eliminar")
            return
        
        # Crear ventana de selección
        dialog = tk.Toplevel(self.root)
        dialog.title("Eliminar Objeto")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="Selecciona el objeto a eliminar:", font=('Arial', 12)).pack(pady=10)
        
        # Lista de objetos
        listbox = tk.Listbox(dialog, height=10)
        listbox.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        for item in items:
            listbox.insert(tk.END, item)
        
        def delete_selected():
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("Advertencia", "Selecciona un objeto para eliminar")
                return
            
            item_name = listbox.get(selection[0])
            
            if messagebox.askyesno("Confirmar", f"¿Estás seguro de eliminar '{item_name}' completamente?\nEsto eliminará todos sus registros."):
                success, message = self.data_manager.delete_item_completely(item_name)
                
                if success:
                    messagebox.showinfo("Éxito", message)
                    dialog.destroy()
                    self.refresh_item_list()
                    self.update_item_info()
                else:
                    messagebox.showerror("Error", message)
        
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Button(button_frame, text="Eliminar", command=delete_selected).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="Cancelar", command=dialog.destroy).pack(side=tk.RIGHT)
    
    def show_rename_dialog(self):
        """Mostrar diálogo para renombrar objetos"""
        items = self.data_manager.get_all_items()
        if not items:
            messagebox.showinfo("Info", "No hay objetos para renombrar")
            return
        
        # Crear ventana de selección
        dialog = tk.Toplevel(self.root)
        dialog.title("Renombrar Objeto")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()
        
        ttk.Label(dialog, text="Objeto actual:", font=('Arial', 10)).grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        
        current_var = tk.StringVar()
        current_combo = ttk.Combobox(dialog, textvariable=current_var, values=items, state="readonly", width=30)
        current_combo.grid(row=0, column=1, padx=10, pady=5)
        
        ttk.Label(dialog, text="Nuevo nombre:", font=('Arial', 10)).grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        
        new_var = tk.StringVar()
        new_entry = ttk.Entry(dialog, textvariable=new_var, width=32)
        new_entry.grid(row=1, column=1, padx=10, pady=5)
        
        def rename_item():
            old_name = current_var.get()
            new_name = new_var.get().strip()
            
            if not old_name or not new_name:
                messagebox.showerror("Error", "Selecciona un objeto y proporciona un nuevo nombre")
                return
            
            success, message = self.data_manager.rename_item(old_name, new_name)
            
            if success:
                messagebox.showinfo("Éxito", message)
                dialog.destroy()
                self.refresh_item_list()
                self.selected_item.set(new_name)
                self.update_item_info()
            else:
                messagebox.showerror("Error", message)
        
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=2, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="Renombrar", command=rename_item).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancelar", command=dialog.destroy).pack(side=tk.LEFT, padx=5)
    
    def create_backup(self):
        """Crear backup de los datos"""
        try:
            backup_file = self.data_manager.backup_data()
            messagebox.showinfo("Éxito", f"Backup creado exitosamente:\n{backup_file}")
        except Exception as e:
            messagebox.showerror("Error", f"Error al crear backup: {str(e)}")

def main():
    root = tk.Tk()
    app = NightCrowsTracker(root)
    root.mainloop()

if __name__ == "__main__":
    main()
