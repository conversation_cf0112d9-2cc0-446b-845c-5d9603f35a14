#!/usr/bin/env python3
"""
Sistema de Notificaciones Móviles para Night Crows Tracker
Incluye notificaciones push, Telegram, Discord y WhatsApp
"""

import requests
import json
import os
from datetime import datetime
import threading
import time
from data_manager import DataManager
from advanced_automation import AdvancedAutomation

class MobileNotifications:
    def __init__(self, config_file="config/notifications_config.json"):
        self.config_file = config_file
        self.data_manager = DataManager()
        self.advanced_automation = AdvancedAutomation()
        self.config = self.load_config()
        
    def load_config(self):
        """Cargar configuración de notificaciones"""
        default_config = {
            "telegram": {
                "enabled": False,
                "bot_token": "",
                "chat_id": "",
                "alerts_enabled": True,
                "signals_enabled": True
            },
            "discord": {
                "enabled": False,
                "webhook_url": "",
                "alerts_enabled": True,
                "signals_enabled": True
            },
            "pushbullet": {
                "enabled": False,
                "api_key": "",
                "alerts_enabled": True,
                "signals_enabled": True
            },
            "ntfy": {
                "enabled": False,
                "topic": "nightcrows_tracker",
                "server": "https://ntfy.sh",
                "alerts_enabled": True,
                "signals_enabled": True
            },
            "notification_settings": {
                "min_confidence": 75,
                "max_notifications_per_hour": 10,
                "quiet_hours": {
                    "enabled": False,
                    "start": "22:00",
                    "end": "08:00"
                }
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Combinar con configuración por defecto
                    for key in default_config:
                        if key not in config:
                            config[key] = default_config[key]
                    return config
            except:
                pass
        
        # Crear archivo de configuración
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        return default_config
    
    def is_quiet_hours(self):
        """Verificar si estamos en horario silencioso"""
        if not self.config['notification_settings']['quiet_hours']['enabled']:
            return False
        
        now = datetime.now().time()
        start_time = datetime.strptime(self.config['notification_settings']['quiet_hours']['start'], '%H:%M').time()
        end_time = datetime.strptime(self.config['notification_settings']['quiet_hours']['end'], '%H:%M').time()
        
        if start_time <= end_time:
            return start_time <= now <= end_time
        else:  # Horario que cruza medianoche
            return now >= start_time or now <= end_time
    
    def send_telegram_notification(self, title, message, priority="normal"):
        """Enviar notificación por Telegram"""
        if not self.config['telegram']['enabled']:
            return False
        
        bot_token = self.config['telegram']['bot_token']
        chat_id = self.config['telegram']['chat_id']
        
        if not bot_token or not chat_id:
            return False
        
        # Formatear mensaje
        emoji = "🚨" if priority == "high" else "📊" if priority == "medium" else "ℹ️"
        text = f"{emoji} *{title}*\n\n{message}"
        
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            'chat_id': chat_id,
            'text': text,
            'parse_mode': 'Markdown'
        }
        
        try:
            response = requests.post(url, data=data, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def send_discord_notification(self, title, message, priority="normal"):
        """Enviar notificación por Discord"""
        if not self.config['discord']['enabled']:
            return False
        
        webhook_url = self.config['discord']['webhook_url']
        if not webhook_url:
            return False
        
        # Colores según prioridad
        colors = {
            "high": 0xFF0000,    # Rojo
            "medium": 0xFFA500,  # Naranja
            "normal": 0x0099FF   # Azul
        }
        
        embed = {
            "title": title,
            "description": message,
            "color": colors.get(priority, colors["normal"]),
            "timestamp": datetime.now().isoformat(),
            "footer": {
                "text": "Night Crows Tracker"
            }
        }
        
        data = {
            "embeds": [embed]
        }
        
        try:
            response = requests.post(webhook_url, json=data, timeout=10)
            return response.status_code == 204
        except:
            return False
    
    def send_pushbullet_notification(self, title, message, priority="normal"):
        """Enviar notificación por Pushbullet"""
        if not self.config['pushbullet']['enabled']:
            return False
        
        api_key = self.config['pushbullet']['api_key']
        if not api_key:
            return False
        
        url = "https://api.pushbullet.com/v2/pushes"
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "type": "note",
            "title": title,
            "body": message
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def send_ntfy_notification(self, title, message, priority="normal"):
        """Enviar notificación por ntfy.sh"""
        if not self.config['ntfy']['enabled']:
            return False
        
        topic = self.config['ntfy']['topic']
        server = self.config['ntfy']['server']
        
        if not topic:
            return False
        
        url = f"{server}/{topic}"
        
        # Mapear prioridades
        ntfy_priority = {
            "high": "urgent",
            "medium": "high", 
            "normal": "default"
        }.get(priority, "default")
        
        headers = {
            "Title": title,
            "Priority": ntfy_priority,
            "Tags": "chart_with_upwards_trend,money_with_wings"
        }
        
        try:
            response = requests.post(url, data=message, headers=headers, timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def send_notification(self, title, message, priority="normal", notification_type="alert"):
        """Enviar notificación a todos los servicios habilitados"""
        if self.is_quiet_hours() and priority != "high":
            return
        
        results = {}
        
        # Telegram
        if self.config['telegram']['enabled'] and self.config['telegram'][f'{notification_type}s_enabled']:
            results['telegram'] = self.send_telegram_notification(title, message, priority)
        
        # Discord
        if self.config['discord']['enabled'] and self.config['discord'][f'{notification_type}s_enabled']:
            results['discord'] = self.send_discord_notification(title, message, priority)
        
        # Pushbullet
        if self.config['pushbullet']['enabled'] and self.config['pushbullet'][f'{notification_type}s_enabled']:
            results['pushbullet'] = self.send_pushbullet_notification(title, message, priority)
        
        # ntfy
        if self.config['ntfy']['enabled'] and self.config['ntfy'][f'{notification_type}s_enabled']:
            results['ntfy'] = self.send_ntfy_notification(title, message, priority)
        
        return results
    
    def process_alerts(self, alerts):
        """Procesar y enviar alertas"""
        for alert in alerts:
            confidence = alert.get('confidence', 0)
            if confidence < self.config['notification_settings']['min_confidence']:
                continue
            
            priority = "high" if alert.get('priority') == 'ALTA' else "medium"
            
            title = f"🚨 Alerta: {alert['item']}"
            message = f"{alert['message']}\n"
            
            if alert.get('action'):
                message += f"Acción recomendada: {alert['action']}\n"
            
            if confidence > 0:
                message += f"Confianza: {confidence:.0f}%"
            
            self.send_notification(title, message, priority, "alert")
    
    def process_signals(self, signals):
        """Procesar y enviar señales de trading"""
        for signal in signals:
            confidence = signal.get('confidence', 0)
            if confidence < self.config['notification_settings']['min_confidence']:
                continue
            
            priority = "high" if confidence > 90 else "medium"
            
            title = f"📈 Señal: {signal['item']}"
            message = f"Señal: {signal['signal']}\n"
            
            if signal.get('reason'):
                message += f"Razón: {signal['reason']}\n"
            
            if confidence > 0:
                message += f"Confianza: {confidence:.0f}%"
            
            self.send_notification(title, message, priority, "signal")
    
    def run_notification_monitor(self):
        """Ejecutar monitor de notificaciones en tiempo real"""
        print("📱 MONITOR DE NOTIFICACIONES MÓVILES INICIADO")
        print("=" * 50)
        
        # Mostrar servicios habilitados
        enabled_services = []
        for service in ['telegram', 'discord', 'pushbullet', 'ntfy']:
            if self.config[service]['enabled']:
                enabled_services.append(service.title())
        
        if enabled_services:
            print(f"📲 Servicios habilitados: {', '.join(enabled_services)}")
        else:
            print("⚠️ No hay servicios de notificación habilitados")
            print("   Edita config/notifications_config.json para configurar")
        
        print("🔄 Monitoreando alertas y señales...")
        print("Presiona Ctrl+C para detener\n")
        
        last_check = datetime.now()
        
        try:
            while True:
                # Generar reporte avanzado
                report = self.advanced_automation.generate_advanced_report()
                
                # Procesar alertas de alta prioridad
                high_priority_alerts = [alert for alert in report['advanced_alerts'] 
                                      if alert.get('priority') == 'ALTA']
                
                if high_priority_alerts:
                    self.process_alerts(high_priority_alerts)
                    print(f"🚨 {len(high_priority_alerts)} alertas enviadas - {datetime.now().strftime('%H:%M:%S')}")
                
                # Procesar señales fuertes
                strong_signals = [signal for signal in report['trading_signals'] 
                                if signal.get('confidence', 0) > 85]
                
                if strong_signals:
                    self.process_signals(strong_signals)
                    print(f"📈 {len(strong_signals)} señales enviadas - {datetime.now().strftime('%H:%M:%S')}")
                
                # Esperar 5 minutos
                time.sleep(300)
                
        except KeyboardInterrupt:
            print("\n⏹️ Monitor de notificaciones detenido")
    
    def test_notifications(self):
        """Probar todas las notificaciones configuradas"""
        print("🧪 PROBANDO NOTIFICACIONES...")
        print("=" * 40)
        
        test_title = "🧪 Prueba - Night Crows Tracker"
        test_message = f"Notificación de prueba enviada el {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        results = self.send_notification(test_title, test_message, "normal", "alert")
        
        for service, success in results.items():
            status = "✅ Éxito" if success else "❌ Error"
            print(f"{service.title()}: {status}")
        
        if not results:
            print("⚠️ No hay servicios configurados")
            print("Edita config/notifications_config.json para configurar notificaciones")
    
    def setup_wizard(self):
        """Asistente de configuración interactivo"""
        print("🔧 ASISTENTE DE CONFIGURACIÓN DE NOTIFICACIONES")
        print("=" * 55)
        
        # Telegram
        print("\n📱 TELEGRAM")
        if input("¿Configurar Telegram? (s/n): ").lower() in ['s', 'si', 'sí']:
            print("\nPara configurar Telegram:")
            print("1. Habla con @BotFather en Telegram")
            print("2. Crea un bot con /newbot")
            print("3. Obtén el token del bot")
            print("4. Habla con @userinfobot para obtener tu chat_id")
            
            bot_token = input("\nToken del bot: ").strip()
            chat_id = input("Chat ID: ").strip()
            
            if bot_token and chat_id:
                self.config['telegram']['enabled'] = True
                self.config['telegram']['bot_token'] = bot_token
                self.config['telegram']['chat_id'] = chat_id
                print("✅ Telegram configurado")
        
        # Discord
        print("\n💬 DISCORD")
        if input("¿Configurar Discord? (s/n): ").lower() in ['s', 'si', 'sí']:
            print("\nPara configurar Discord:")
            print("1. Ve a Configuración del servidor > Integraciones > Webhooks")
            print("2. Crea un nuevo webhook")
            print("3. Copia la URL del webhook")
            
            webhook_url = input("\nURL del webhook: ").strip()
            
            if webhook_url:
                self.config['discord']['enabled'] = True
                self.config['discord']['webhook_url'] = webhook_url
                print("✅ Discord configurado")
        
        # ntfy.sh (más fácil)
        print("\n🔔 NTFY.SH (Recomendado - Fácil)")
        if input("¿Configurar ntfy.sh? (s/n): ").lower() in ['s', 'si', 'sí']:
            print("\nPara usar ntfy.sh:")
            print("1. Instala la app ntfy desde Google Play o App Store")
            print("2. Suscríbete al tema que elijas")
            
            topic = input("Nombre del tema (ej: mi_nightcrows_123): ").strip()
            
            if topic:
                self.config['ntfy']['enabled'] = True
                self.config['ntfy']['topic'] = topic
                print("✅ ntfy.sh configurado")
                print(f"📱 Suscríbete al tema '{topic}' en la app ntfy")
        
        # Guardar configuración
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
        
        print("\n✅ Configuración guardada")
        print("🧪 Ejecuta 'python mobile_notifications.py test' para probar")

def main():
    """Función principal"""
    import sys
    
    notifications = MobileNotifications()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'monitor':
            notifications.run_notification_monitor()
        elif command == 'test':
            notifications.test_notifications()
        elif command == 'setup':
            notifications.setup_wizard()
        else:
            print("Comandos disponibles:")
            print("  python mobile_notifications.py monitor  - Iniciar monitor")
            print("  python mobile_notifications.py test     - Probar notificaciones")
            print("  python mobile_notifications.py setup    - Configurar servicios")
    else:
        print("📱 NOTIFICACIONES MÓVILES - Night Crows")
        print("1. Configurar servicios de notificación")
        print("2. Probar notificaciones")
        print("3. Iniciar monitor en tiempo real")
        
        choice = input("\nSelecciona una opción (1-3): ")
        
        if choice == '1':
            notifications.setup_wizard()
        elif choice == '2':
            notifications.test_notifications()
        elif choice == '3':
            notifications.run_notification_monitor()
        else:
            print("Opción no válida")

if __name__ == "__main__":
    main()
