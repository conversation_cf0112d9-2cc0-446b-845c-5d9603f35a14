#!/usr/bin/env python3
"""
Script de configuración automática para Night Crows Tracker
Configura todas las funciones automáticas de manera sencilla
"""

import json
import os
from datetime import datetime
import pandas as pd

def create_directories():
    """Crear directorios necesarios"""
    directories = [
        'config',
        'data',
        'reports',
        'auto_charts',
        'alerts'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Directorio creado: {directory}/")

def create_sample_data():
    """Crear datos de ejemplo para demostración"""
    sample_items = [
        {"name": "Espada de Hierro", "base_price": 100, "variation": 20},
        {"name": "Armadura de Cuero", "base_price": 75, "variation": 15},
        {"name": "Poción de Vida", "base_price": 15, "variation": 5},
        {"name": "Gema Mágica", "base_price": 200, "variation": 50},
        {"name": "Arco Élfico", "base_price": 150, "variation": 30}
    ]
    
    # Crear CSV de ejemplo
    csv_data = []
    servers = ["Servidor 1", "Servidor 2", None]
    
    for item in sample_items:
        for day in range(7):  # 7 días de datos
            date = (datetime.now() - pd.Timedelta(days=6-day)).strftime('%Y-%m-%d')
            price = item["base_price"] + (day - 3) * (item["variation"] / 10)  # Variación gradual
            
            csv_data.append({
                'item_name': item["name"],
                'price': round(price, 2),
                'quantity': 5 + (day % 3),
                'server': servers[day % 3],
                'date': date,
                'notes': f'Precio día {day + 1}'
            })
    
    df = pd.DataFrame(csv_data)
    df.to_csv('data/sample_data.csv', index=False)
    print("✅ Archivo de datos de ejemplo creado: data/sample_data.csv")
    
    return len(csv_data)

def create_automation_config():
    """Crear configuración de automatización"""
    config = {
        "auto_alerts": {
            "enabled": True,
            "price_change_threshold": 15,
            "email_notifications": False,
            "email_config": {
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "email": "<EMAIL>",
                "password": "tu_password_de_aplicacion",
                "recipient": "<EMAIL>"
            }
        },
        "auto_analysis": {
            "enabled": True,
            "daily_report": True,
            "weekly_summary": True
        },
        "data_import": {
            "enabled": True,
            "csv_file": "data/sample_data.csv",
            "api_endpoint": ""
        },
        "watched_items": [
            "Espada de Hierro",
            "Armadura de Cuero",
            "Poción de Vida",
            "Gema Mágica",
            "Arco Élfico"
        ]
    }
    
    with open('config/auto_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ Configuración de automatización creada: config/auto_config.json")
    return config

def create_batch_files():
    """Crear archivos batch para Windows para ejecución fácil"""
    
    # Archivo para ejecutar la aplicación principal
    main_bat = """@echo off
echo Iniciando Night Crows Tracker...
python main.py
pause
"""
    
    with open('ejecutar_tracker.bat', 'w') as f:
        f.write(main_bat)
    
    # Archivo para modo automático
    auto_bat = """@echo off
echo Iniciando modo automatico...
python auto_tracker.py schedule
pause
"""
    
    with open('ejecutar_automatico.bat', 'w') as f:
        f.write(auto_bat)
    
    # Archivo para verificación manual
    check_bat = """@echo off
echo Ejecutando verificacion manual...
python auto_tracker.py check
pause
"""
    
    with open('verificar_precios.bat', 'w') as f:
        f.write(check_bat)
    
    # Archivo para importar datos
    import_bat = """@echo off
echo Importando datos desde CSV...
python auto_tracker.py import
pause
"""
    
    with open('importar_datos.bat', 'w') as f:
        f.write(import_bat)
    
    print("✅ Archivos .bat creados para ejecución fácil:")
    print("   - ejecutar_tracker.bat (aplicación principal)")
    print("   - ejecutar_automatico.bat (modo automático)")
    print("   - verificar_precios.bat (verificación manual)")
    print("   - importar_datos.bat (importar datos)")

def create_readme_automation():
    """Crear README específico para automatización"""
    readme_content = """# 🤖 AUTOMATIZACIÓN - Night Crows Tracker

## 🚀 Inicio Rápido

### Opción 1: Archivos .bat (Windows)
- **ejecutar_tracker.bat** - Abrir la aplicación principal
- **ejecutar_automatico.bat** - Iniciar modo automático
- **verificar_precios.bat** - Verificación manual de precios
- **importar_datos.bat** - Importar datos desde CSV

### Opción 2: Línea de comandos
```bash
# Aplicación principal
python main.py

# Modo automático (ejecuta tareas programadas)
python auto_tracker.py schedule

# Verificación manual
python auto_tracker.py check

# Importar datos
python auto_tracker.py import
```

## 🔧 Funciones Automáticas

### 1. **Alertas de Precios**
- ✅ Monitoreo automático cada hora
- ✅ Alertas cuando el precio cambia más del 15%
- ✅ Recomendaciones automáticas de compra/venta
- ✅ Notificaciones por email (opcional)

### 2. **Reportes Automáticos**
- ✅ Reporte diario a las 8:00 AM
- ✅ Gráficos automáticos generados
- ✅ Resumen de tendencias
- ✅ Archivos guardados en carpeta `reports/`

### 3. **Importación de Datos**
- ✅ Importación automática desde CSV
- ✅ Plantilla CSV incluida
- ✅ Procesamiento diario a las 9:00 AM

### 4. **Análisis Inteligente**
- ✅ Cálculo automático de tendencias
- ✅ Detección de patrones de precios
- ✅ Recomendaciones basadas en estadísticas

## 📊 Importar Datos Masivamente

### Usar el archivo CSV de ejemplo:
1. Edita `data/sample_data.csv` con tus datos reales
2. Ejecuta `importar_datos.bat` o `python auto_tracker.py import`

### Formato del CSV:
```csv
item_name,price,quantity,server,date,notes
Espada de Hierro,105.50,3,Servidor 1,2024-01-15,Precio matutino
Armadura de Cuero,78.25,5,Servidor 2,2024-01-15,Precio vespertino
```

## ⚙️ Configuración

### Editar configuración automática:
Archivo: `config/auto_config.json`

```json
{
  "auto_alerts": {
    "enabled": true,
    "price_change_threshold": 15,
    "email_notifications": false
  },
  "watched_items": [
    "Espada de Hierro",
    "Armadura de Cuero"
  ]
}
```

### Configurar notificaciones por email:
1. Edita `config/auto_config.json`
2. Cambia `email_notifications` a `true`
3. Configura tu email y contraseña de aplicación

## 📈 Archivos Generados Automáticamente

- `reports/daily_YYYYMMDD.json` - Reportes diarios
- `auto_charts/YYYYMMDD/` - Gráficos automáticos
- `data/alerts_YYYYMMDD.json` - Historial de alertas

## 🎯 Casos de Uso

### Para traders activos:
```bash
# Ejecutar modo automático en segundo plano
python auto_tracker.py schedule
```

### Para análisis diario:
```bash
# Verificación rápida manual
python auto_tracker.py check
```

### Para importar datos históricos:
```bash
# Crear plantilla CSV
python auto_tracker.py template

# Importar datos
python auto_tracker.py import mi_archivo.csv
```

## 🔄 Flujo de Trabajo Recomendado

1. **Configuración inicial**: Ejecutar `setup_automation.py`
2. **Datos históricos**: Importar CSV con datos pasados
3. **Modo automático**: Dejar corriendo `ejecutar_automatico.bat`
4. **Revisión diaria**: Verificar reportes en carpeta `reports/`
5. **Alertas**: Revisar alertas importantes para trading

¡El sistema ahora trabaja por ti! 🚀
"""
    
    with open('AUTOMATIZACION.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ Guía de automatización creada: AUTOMATIZACION.md")

def main():
    """Configuración principal"""
    print("CONFIGURACION AUTOMATICA - Night Crows Tracker")
    print("=" * 60)
    
    print("\n1. Creando directorios necesarios...")
    create_directories()
    
    print("\n2. Creando datos de ejemplo...")
    sample_count = create_sample_data()
    print(f"   📊 {sample_count} registros de ejemplo creados")
    
    print("\n3. Configurando automatización...")
    config = create_automation_config()
    print(f"   🎯 {len(config['watched_items'])} objetos configurados para monitoreo")
    
    print("\n4. Creando archivos de ejecución...")
    create_batch_files()
    
    print("\n5. Creando documentación...")
    create_readme_automation()
    
    print("\n" + "=" * 60)
    print("✅ CONFIGURACIÓN COMPLETADA")
    print("=" * 60)
    
    print("\n🚀 PRÓXIMOS PASOS:")
    print("1. Ejecuta: ejecutar_tracker.bat (aplicación principal)")
    print("2. O ejecuta: ejecutar_automatico.bat (modo automático)")
    print("3. Lee: AUTOMATIZACION.md (guía completa)")
    
    print("\n📊 DATOS DE PRUEBA:")
    print("- Datos de ejemplo importados automáticamente")
    print("- 5 objetos con 7 días de historial cada uno")
    print("- Listo para probar todas las funciones")
    
    print("\n🔧 PERSONALIZACIÓN:")
    print("- Edita: config/auto_config.json (configuración)")
    print("- Edita: data/sample_data.csv (tus datos reales)")
    
    # Preguntar si quiere importar los datos de ejemplo
    response = input("\n¿Quieres importar los datos de ejemplo ahora? (s/n): ").lower()
    if response in ['s', 'si', 'sí', 'y', 'yes']:
        try:
            from auto_tracker import AutoTracker
            tracker = AutoTracker()
            tracker.import_from_csv('data/sample_data.csv')
            print("✅ Datos de ejemplo importados correctamente")
        except Exception as e:
            print(f"⚠️ Error al importar datos: {e}")
            print("   Puedes importarlos manualmente después con: python auto_tracker.py import")
    
    print("\n🎉 ¡Todo listo! El tracker automático está configurado.")

if __name__ == "__main__":
    main()
