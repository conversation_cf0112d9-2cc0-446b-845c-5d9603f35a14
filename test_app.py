#!/usr/bin/env python3
"""
Script de pruebas para el rastreador de precios de Night Crows
"""

import os
import json
import sys
from datetime import datetime, timedelta
import random

# Importar nuestros módulos
from data_manager import DataManager
from chart_generator import ChartGenerator

def test_data_manager():
    """Probar funcionalidades del DataManager"""
    print("🧪 Probando DataManager...")
    
    # Crear instancia de prueba
    dm = DataManager("data/test_data.json")
    
    # Datos de prueba
    test_items = [
        ("Espada de Hierro", [100, 95, 110, 105, 98, 120, 115]),
        ("Armadura de Cuero", [50, 55, 48, 52, 60, 58, 62]),
        ("Poción de Vida", [10, 12, 9, 11, 13, 10, 14]),
        ("Gema Mágica", [200, 180, 220, 210, 190, 230, 225])
    ]
    
    # Agregar datos de prueba
    for item_name, prices in test_items:
        for i, price in enumerate(prices):
            # Simular fechas de los últimos 7 días
            test_date = datetime.now() - timedelta(days=len(prices)-1-i)
            
            # Agregar datos con fechas simuladas
            dm.add_item_data(item_name, price, 
                           quantity=random.randint(1, 10),
                           server=random.choice(["Servidor 1", "Servidor 2", None]),
                           notes=f"Precio del día {i+1}")
    
    print(f"✅ Agregados {len(test_items)} objetos con datos históricos")
    
    # Probar estadísticas
    for item_name, _ in test_items:
        stats = dm.get_price_statistics(item_name)
        recommendation = dm.get_buy_sell_recommendation(item_name)
        
        print(f"\n📊 {item_name}:")
        print(f"   Precio actual: {stats['current_price']:.2f}")
        print(f"   Promedio: {stats['average_price']:.2f}")
        print(f"   Tendencia: {stats['price_trend']}")
        print(f"   Recomendación: {recommendation}")
    
    return dm

def test_chart_generator(data_manager):
    """Probar generación de gráficos"""
    print("\n🧪 Probando ChartGenerator...")
    
    cg = ChartGenerator()
    
    # Crear directorio para gráficos de prueba
    os.makedirs("test_charts", exist_ok=True)
    
    # Probar gráfico de precios
    items = data_manager.get_all_items()
    if items:
        test_item = items[0]
        print(f"📈 Generando gráfico de precios para {test_item}...")
        
        fig = cg.create_price_chart(data_manager, test_item, days=30)
        if fig:
            cg.save_chart(fig, f"test_charts/{test_item}_precios.png")
            print(f"✅ Gráfico de precios guardado")
        
        # Probar gráfico de estadísticas
        print(f"📊 Generando gráfico de estadísticas para {test_item}...")
        fig_stats = cg.create_statistics_chart(data_manager, test_item)
        if fig_stats:
            cg.save_chart(fig_stats, f"test_charts/{test_item}_estadisticas.png")
            print(f"✅ Gráfico de estadísticas guardado")
        
        # Probar gráfico de comparación
        if len(items) >= 2:
            print(f"📈 Generando gráfico de comparación...")
            fig_comp = cg.create_comparison_chart(data_manager, items[:3], days=30)
            if fig_comp:
                cg.save_chart(fig_comp, "test_charts/comparacion.png")
                print(f"✅ Gráfico de comparación guardado")

def test_data_persistence():
    """Probar persistencia de datos"""
    print("\n🧪 Probando persistencia de datos...")
    
    # Crear nueva instancia para verificar que los datos persisten
    dm2 = DataManager("data/test_data.json")
    items = dm2.get_all_items()
    
    if items:
        print(f"✅ Datos persistidos correctamente: {len(items)} objetos encontrados")
        
        # Verificar integridad de datos
        for item in items[:2]:  # Verificar primeros 2 objetos
            data = dm2.get_item_data(item)
            print(f"   {item}: {len(data)} registros")
    else:
        print("❌ Error: No se encontraron datos persistidos")

def test_error_handling():
    """Probar manejo de errores"""
    print("\n🧪 Probando manejo de errores...")
    
    dm = DataManager("data/test_data.json")
    
    # Probar con objeto inexistente
    stats = dm.get_price_statistics("Objeto Inexistente")
    if stats is None:
        print("✅ Manejo correcto de objeto inexistente")
    
    # Probar con datos vacíos
    empty_df = dm.get_item_dataframe("Objeto Inexistente")
    if empty_df.empty:
        print("✅ Manejo correcto de DataFrame vacío")

def generate_test_report():
    """Generar reporte de pruebas"""
    print("\n" + "="*50)
    print("📋 REPORTE DE PRUEBAS COMPLETADAS")
    print("="*50)
    
    tests_passed = []
    
    # Verificar archivos creados
    if os.path.exists("data/test_data.json"):
        tests_passed.append("✅ Almacenamiento de datos")
    
    if os.path.exists("test_charts"):
        chart_files = [f for f in os.listdir("test_charts") if f.endswith('.png')]
        if chart_files:
            tests_passed.append(f"✅ Generación de gráficos ({len(chart_files)} archivos)")
    
    # Verificar funcionalidades
    dm = DataManager("data/test_data.json")
    if dm.get_all_items():
        tests_passed.append("✅ Gestión de datos")
        tests_passed.append("✅ Cálculo de estadísticas")
        tests_passed.append("✅ Recomendaciones de compra/venta")
    
    print("\nPruebas exitosas:")
    for test in tests_passed:
        print(f"  {test}")
    
    print(f"\nTotal: {len(tests_passed)} pruebas pasaron correctamente")
    
    return len(tests_passed)

def main():
    """Ejecutar todas las pruebas"""
    print("🚀 Iniciando pruebas exhaustivas del Rastreador de Night Crows")
    print("="*60)
    
    try:
        # Ejecutar pruebas
        dm = test_data_manager()
        test_chart_generator(dm)
        test_data_persistence()
        test_error_handling()
        
        # Generar reporte
        tests_passed = generate_test_report()
        
        print(f"\n🎉 Pruebas completadas exitosamente!")
        print(f"📁 Archivos de prueba creados en: data/ y test_charts/")
        
        return tests_passed > 0
        
    except Exception as e:
        print(f"\n❌ Error durante las pruebas: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
