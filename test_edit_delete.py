#!/usr/bin/env python3
"""
Prueba rápida de las funciones de editar y eliminar
"""

from data_manager import DataManager

def test_edit_delete_functions():
    """Probar las nuevas funciones de editar y eliminar"""
    print("🧪 PROBANDO FUNCIONES DE EDITAR Y ELIMINAR")
    print("=" * 50)
    
    # Crear instancia del data manager
    dm = DataManager()
    
    # Agregar algunos datos de prueba
    print("\n1. Agregando datos de prueba...")
    dm.add_item_data("Espada de Prueba", 100.0, 5, "Servidor Test", "Prueba inicial")
    dm.add_item_data("Espada de Prueba", 105.0, 3, "Servidor Test", "Segundo registro")
    dm.add_item_data("Armadura de Prueba", 75.0, 2, "Servidor Test", "Armadura inicial")
    
    print("✅ Datos agregados")
    
    # Mostrar datos actuales
    print("\n2. Datos actuales:")
    items = dm.get_all_items()
    for item in items:
        entries = dm.get_item_data(item)
        print(f"   {item}: {len(entries)} registros")
        for i, entry in enumerate(entries):
            print(f"     [{i}] {entry['date']}: ${entry['price']:.2f}")
    
    # Probar edición de entrada
    print("\n3. Probando edición de entrada...")
    success, message = dm.edit_item_entry("Espada de Prueba", 0, new_price=110.0, new_notes="Precio editado")
    print(f"   Resultado: {message}")
    
    # Mostrar datos después de edición
    print("\n4. Datos después de edición:")
    entries = dm.get_item_data("Espada de Prueba")
    for i, entry in enumerate(entries):
        print(f"   [{i}] {entry['date']}: ${entry['price']:.2f} - {entry['notes']}")
    
    # Probar eliminación de entrada
    print("\n5. Probando eliminación de entrada...")
    success, message = dm.delete_item_entry("Espada de Prueba", 1)
    print(f"   Resultado: {message}")
    
    # Mostrar datos después de eliminación
    print("\n6. Datos después de eliminación:")
    entries = dm.get_item_data("Espada de Prueba")
    print(f"   Espada de Prueba ahora tiene {len(entries)} registros")
    
    # Probar renombrar objeto
    print("\n7. Probando renombrar objeto...")
    success, message = dm.rename_item("Armadura de Prueba", "Armadura Renombrada")
    print(f"   Resultado: {message}")
    
    # Mostrar objetos después de renombrar
    print("\n8. Objetos después de renombrar:")
    items = dm.get_all_items()
    for item in items:
        print(f"   - {item}")
    
    # Probar backup
    print("\n9. Probando backup...")
    backup_file = dm.backup_data()
    print(f"   Backup creado: {backup_file}")
    
    # Probar búsqueda
    print("\n10. Probando búsqueda...")
    results = dm.search_items("espada")
    print(f"   Búsqueda 'espada': {results}")
    
    # Probar eliminación completa
    print("\n11. Probando eliminación completa...")
    success, message = dm.delete_item_completely("Espada de Prueba")
    print(f"   Resultado: {message}")
    
    # Mostrar objetos finales
    print("\n12. Objetos finales:")
    items = dm.get_all_items()
    for item in items:
        print(f"   - {item}")
    
    print("\n✅ TODAS LAS PRUEBAS COMPLETADAS")
    print("=" * 50)

if __name__ == "__main__":
    test_edit_delete_functions()
