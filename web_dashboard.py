#!/usr/bin/env python3
"""
Dashboard Web en Tiempo Real para Night Crows Tracker
Incluye gráficos interactivos, alertas en vivo y API REST
"""

from flask import Flask, render_template, jsonify, request, send_file
import json
import os
from datetime import datetime, timedelta
import threading
import time
from data_manager import DataManager
from chart_generator import ChartGenerator
from advanced_automation import AdvancedAutomation
import plotly.graph_objs as go
import plotly.utils

app = Flask(__name__)
data_manager = DataManager()
chart_generator = ChartGenerator()
advanced_automation = AdvancedAutomation()

# Variables globales para datos en tiempo real
live_data = {
    'alerts': [],
    'signals': [],
    'last_update': None
}

def update_live_data():
    """Actualizar datos en tiempo real cada minuto"""
    while True:
        try:
            # Generar reporte avanzado
            report = advanced_automation.generate_advanced_report()
            
            # Actualizar datos globales
            live_data['alerts'] = report['advanced_alerts']
            live_data['signals'] = report['trading_signals']
            live_data['last_update'] = datetime.now().isoformat()
            
            print(f"📊 Datos actualizados: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            print(f"❌ Error actualizando datos: {e}")
        
        time.sleep(60)  # Actualizar cada minuto

# Iniciar hilo de actualización en segundo plano
update_thread = threading.Thread(target=update_live_data, daemon=True)
update_thread.start()

@app.route('/')
def dashboard():
    """Dashboard principal"""
    return render_template('dashboard.html')

@app.route('/api/items')
def api_items():
    """API: Lista de todos los objetos"""
    items = data_manager.get_all_items()
    items_data = []
    
    for item in items:
        stats = data_manager.get_price_statistics(item)
        if stats:
            items_data.append({
                'name': item,
                'current_price': stats['current_price'],
                'average_price': stats['average_price'],
                'trend': stats['price_trend'],
                'recommendation': data_manager.get_buy_sell_recommendation(item)
            })
    
    return jsonify(items_data)

@app.route('/api/item/<item_name>')
def api_item_details(item_name):
    """API: Detalles de un objeto específico"""
    stats = data_manager.get_price_statistics(item_name)
    if not stats:
        return jsonify({'error': 'Item not found'}), 404
    
    # Obtener datos históricos
    df = data_manager.get_item_dataframe(item_name)
    history = []
    if not df.empty:
        for _, row in df.iterrows():
            history.append({
                'date': row['date'].strftime('%Y-%m-%d'),
                'price': row['price'],
                'quantity': row.get('quantity'),
                'server': row.get('server')
            })
    
    # Obtener predicción
    prediction = advanced_automation.predict_prices_ai(item_name, days_ahead=7)
    
    return jsonify({
        'name': item_name,
        'statistics': stats,
        'recommendation': data_manager.get_buy_sell_recommendation(item_name),
        'history': history,
        'prediction': prediction
    })

@app.route('/api/chart/<item_name>')
def api_chart_data(item_name):
    """API: Datos para gráficos interactivos"""
    df = data_manager.get_item_dataframe(item_name)
    if df.empty:
        return jsonify({'error': 'No data available'}), 404
    
    # Crear gráfico con Plotly
    fig = go.Figure()
    
    # Línea de precios
    fig.add_trace(go.Scatter(
        x=df['date'],
        y=df['price'],
        mode='lines+markers',
        name='Precio',
        line=dict(color='#2E86AB', width=3),
        marker=dict(size=6)
    ))
    
    # Línea de promedio
    avg_price = df['price'].mean()
    fig.add_hline(y=avg_price, line_dash="dash", line_color="#A23B72", 
                  annotation_text=f"Promedio: {avg_price:.2f}")
    
    # Configurar layout
    fig.update_layout(
        title=f'Evolución de Precios - {item_name}',
        xaxis_title='Fecha',
        yaxis_title='Precio',
        hovermode='x unified',
        template='plotly_white'
    )
    
    return jsonify(json.loads(plotly.utils.PlotlyJSONEncoder().encode(fig)))

@app.route('/api/alerts')
def api_alerts():
    """API: Alertas en tiempo real"""
    return jsonify({
        'alerts': live_data['alerts'],
        'last_update': live_data['last_update']
    })

@app.route('/api/signals')
def api_signals():
    """API: Señales de trading"""
    return jsonify({
        'signals': live_data['signals'],
        'last_update': live_data['last_update']
    })

@app.route('/api/add_price', methods=['POST'])
def api_add_price():
    """API: Agregar nuevo precio"""
    data = request.json
    
    try:
        data_manager.add_item_data(
            item_name=data['item_name'],
            price=float(data['price']),
            quantity=data.get('quantity'),
            server=data.get('server'),
            notes=data.get('notes', '')
        )
        return jsonify({'success': True, 'message': 'Precio agregado correctamente'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/portfolio')
def api_portfolio():
    """API: Datos del portafolio"""
    performance = advanced_automation.calculate_portfolio_performance()
    return jsonify(performance)

def create_html_template():
    """Crear template HTML para el dashboard"""
    template_dir = 'templates'
    os.makedirs(template_dir, exist_ok=True)
    
    html_content = '''
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Night Crows - Dashboard en Tiempo Real</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .card { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); margin-bottom: 1rem; }
        .alert-high { border-left: 4px solid #dc3545; }
        .alert-medium { border-left: 4px solid #ffc107; }
        .signal-buy { color: #28a745; font-weight: bold; }
        .signal-sell { color: #dc3545; font-weight: bold; }
        .live-indicator { 
            display: inline-block; 
            width: 10px; 
            height: 10px; 
            background-color: #28a745; 
            border-radius: 50%; 
            animation: blink 1s infinite; 
        }
        @keyframes blink { 0%, 50% { opacity: 1; } 51%, 100% { opacity: 0.3; } }
        .chart-container { height: 400px; }
    </style>
</head>
<body>
    <nav class="navbar navbar-dark bg-dark">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-chart-line"></i> Night Crows - Dashboard en Tiempo Real
                <span class="live-indicator ms-2"></span>
            </span>
            <span class="navbar-text" id="last-update">Cargando...</span>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <div class="row">
            <!-- Panel de Alertas -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle"></i> Alertas en Tiempo Real</h5>
                    </div>
                    <div class="card-body" id="alerts-container" style="max-height: 300px; overflow-y: auto;">
                        <div class="text-center">
                            <div class="spinner-border" role="status"></div>
                            <p>Cargando alertas...</p>
                        </div>
                    </div>
                </div>

                <!-- Panel de Señales -->
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-chart-bar"></i> Señales de Trading</h5>
                    </div>
                    <div class="card-body" id="signals-container" style="max-height: 300px; overflow-y: auto;">
                        <div class="text-center">
                            <div class="spinner-border" role="status"></div>
                            <p>Cargando señales...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Panel Principal -->
            <div class="col-md-8">
                <!-- Selector de Objeto -->
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <select class="form-select" id="item-selector">
                                    <option value="">Selecciona un objeto...</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-primary" onclick="refreshData()">
                                    <i class="fas fa-sync-alt"></i> Actualizar
                                </button>
                                <button class="btn btn-success" onclick="showAddPriceModal()">
                                    <i class="fas fa-plus"></i> Agregar Precio
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gráfico Principal -->
                <div class="card">
                    <div class="card-header">
                        <h5 id="chart-title">Selecciona un objeto para ver el gráfico</h5>
                    </div>
                    <div class="card-body">
                        <div id="price-chart" class="chart-container"></div>
                    </div>
                </div>

                <!-- Estadísticas del Objeto -->
                <div class="card">
                    <div class="card-header">
                        <h5>Estadísticas y Recomendaciones</h5>
                    </div>
                    <div class="card-body" id="item-stats">
                        <p class="text-muted">Selecciona un objeto para ver las estadísticas</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Objetos -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Resumen de Todos los Objetos</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="items-table">
                                <thead>
                                    <tr>
                                        <th>Objeto</th>
                                        <th>Precio Actual</th>
                                        <th>Promedio</th>
                                        <th>Tendencia</th>
                                        <th>Recomendación</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="5" class="text-center">Cargando datos...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para Agregar Precio -->
    <div class="modal fade" id="addPriceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Agregar Nuevo Precio</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addPriceForm">
                        <div class="mb-3">
                            <label class="form-label">Objeto</label>
                            <input type="text" class="form-control" id="modal-item-name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Precio</label>
                            <input type="number" step="0.01" class="form-control" id="modal-price" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Cantidad (opcional)</label>
                            <input type="number" class="form-control" id="modal-quantity">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Servidor (opcional)</label>
                            <input type="text" class="form-control" id="modal-server">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Notas (opcional)</label>
                            <textarea class="form-control" id="modal-notes"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="addPrice()">Agregar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedItem = '';
        
        // Cargar datos iniciales
        $(document).ready(function() {
            loadItems();
            loadAlerts();
            loadSignals();
            
            // Actualizar cada 30 segundos
            setInterval(function() {
                loadAlerts();
                loadSignals();
                if (selectedItem) {
                    loadItemChart(selectedItem);
                }
            }, 30000);
        });
        
        function loadItems() {
            $.get('/api/items', function(data) {
                const selector = $('#item-selector');
                const tbody = $('#items-table tbody');
                
                selector.empty().append('<option value="">Selecciona un objeto...</option>');
                tbody.empty();
                
                data.forEach(function(item) {
                    selector.append(`<option value="${item.name}">${item.name}</option>`);
                    
                    const trendIcon = item.trend === 'Subiendo' ? '📈' : 
                                    item.trend === 'Bajando' ? '📉' : '➡️';
                    
                    tbody.append(`
                        <tr>
                            <td><strong>${item.name}</strong></td>
                            <td>$${item.current_price.toFixed(2)}</td>
                            <td>$${item.average_price.toFixed(2)}</td>
                            <td>${trendIcon} ${item.trend}</td>
                            <td><span class="badge bg-info">${item.recommendation}</span></td>
                        </tr>
                    `);
                });
            });
        }
        
        function loadAlerts() {
            $.get('/api/alerts', function(data) {
                const container = $('#alerts-container');
                container.empty();
                
                if (data.alerts.length === 0) {
                    container.append('<p class="text-muted">No hay alertas activas</p>');
                    return;
                }
                
                data.alerts.forEach(function(alert) {
                    const priorityClass = alert.priority === 'ALTA' ? 'alert-high' : 'alert-medium';
                    const icon = alert.priority === 'ALTA' ? 'fas fa-exclamation-circle' : 'fas fa-info-circle';
                    
                    container.append(`
                        <div class="alert alert-warning ${priorityClass} mb-2">
                            <i class="${icon}"></i>
                            <strong>${alert.item}</strong><br>
                            ${alert.message}
                            ${alert.action ? `<br><small class="text-muted">Acción: ${alert.action}</small>` : ''}
                        </div>
                    `);
                });
                
                $('#last-update').text(`Última actualización: ${new Date(data.last_update).toLocaleTimeString()}`);
            });
        }
        
        function loadSignals() {
            $.get('/api/signals', function(data) {
                const container = $('#signals-container');
                container.empty();
                
                if (data.signals.length === 0) {
                    container.append('<p class="text-muted">No hay señales activas</p>');
                    return;
                }
                
                data.signals.forEach(function(signal) {
                    const signalClass = signal.signal.includes('COMPRA') ? 'signal-buy' : 'signal-sell';
                    const icon = signal.signal.includes('COMPRA') ? 'fas fa-arrow-up' : 'fas fa-arrow-down';
                    
                    container.append(`
                        <div class="card mb-2">
                            <div class="card-body p-2">
                                <div class="d-flex justify-content-between">
                                    <strong>${signal.item}</strong>
                                    <span class="${signalClass}">
                                        <i class="${icon}"></i> ${signal.signal}
                                    </span>
                                </div>
                                ${signal.confidence ? `<small class="text-muted">Confianza: ${signal.confidence.toFixed(0)}%</small>` : ''}
                                ${signal.reason ? `<br><small>${signal.reason}</small>` : ''}
                            </div>
                        </div>
                    `);
                });
            });
        }
        
        $('#item-selector').change(function() {
            selectedItem = $(this).val();
            if (selectedItem) {
                loadItemChart(selectedItem);
                loadItemStats(selectedItem);
            }
        });
        
        function loadItemChart(itemName) {
            $.get(`/api/chart/${itemName}`, function(data) {
                $('#chart-title').text(`Evolución de Precios - ${itemName}`);
                Plotly.newPlot('price-chart', data.data, data.layout, {responsive: true});
            });
        }
        
        function loadItemStats(itemName) {
            $.get(`/api/item/${itemName}`, function(data) {
                const stats = data.statistics;
                const prediction = data.prediction;
                
                let html = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Estadísticas Actuales</h6>
                            <ul class="list-unstyled">
                                <li><strong>Precio Actual:</strong> $${stats.current_price.toFixed(2)}</li>
                                <li><strong>Promedio:</strong> $${stats.average_price.toFixed(2)}</li>
                                <li><strong>Mínimo:</strong> $${stats.min_price.toFixed(2)}</li>
                                <li><strong>Máximo:</strong> $${stats.max_price.toFixed(2)}</li>
                                <li><strong>Tendencia:</strong> ${stats.price_trend}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Recomendación</h6>
                            <div class="alert alert-info">
                                ${data.recommendation}
                            </div>
                `;
                
                if (prediction && prediction.confidence > 70) {
                    html += `
                            <h6>Predicción IA</h6>
                            <p><strong>Próximos 3 días:</strong></p>
                            <ul class="list-unstyled">
                    `;
                    
                    prediction.predictions.slice(0, 3).forEach((price, index) => {
                        html += `<li>Día ${index + 1}: $${price.toFixed(2)}</li>`;
                    });
                    
                    html += `
                            </ul>
                            <small class="text-muted">Confianza: ${prediction.confidence.toFixed(1)}%</small>
                    `;
                }
                
                html += `
                        </div>
                    </div>
                `;
                
                $('#item-stats').html(html);
            });
        }
        
        function refreshData() {
            loadItems();
            loadAlerts();
            loadSignals();
            if (selectedItem) {
                loadItemChart(selectedItem);
                loadItemStats(selectedItem);
            }
        }
        
        function showAddPriceModal() {
            $('#addPriceModal').modal('show');
        }
        
        function addPrice() {
            const data = {
                item_name: $('#modal-item-name').val(),
                price: parseFloat($('#modal-price').val()),
                quantity: $('#modal-quantity').val() || null,
                server: $('#modal-server').val() || null,
                notes: $('#modal-notes').val() || ''
            };
            
            $.ajax({
                url: '/api/add_price',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(response) {
                    $('#addPriceModal').modal('hide');
                    $('#addPriceForm')[0].reset();
                    refreshData();
                    alert('Precio agregado correctamente');
                },
                error: function(xhr) {
                    alert('Error: ' + xhr.responseJSON.error);
                }
            });
        }
    </script>
</body>
</html>
    '''
    
    with open(os.path.join(template_dir, 'dashboard.html'), 'w', encoding='utf-8') as f:
        f.write(html_content)

def main():
    """Ejecutar servidor web"""
    create_html_template()
    
    print("🌐 DASHBOARD WEB INICIADO")
    print("=" * 50)
    print("📊 Dashboard disponible en: http://localhost:5000")
    print("🔄 Datos actualizándose automáticamente cada minuto")
    print("📱 Compatible con móviles y tablets")
    print("🚨 Alertas y señales en tiempo real")
    print("\nPresiona Ctrl+C para detener")
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n⏹️ Servidor web detenido")

if __name__ == "__main__":
    main()
